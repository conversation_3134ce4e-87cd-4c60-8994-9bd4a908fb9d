"""
Test configuration constants and mappings for the Crucible test orchestrator.

This module centralizes all test-related configuration, mappings, and constants
to support modularity and maintainability in the testing framework.
"""

from typing import Dict, List, Set, Any
import platform

# Test path constants
TYPE_A_TEST_PATHS_QUALIFIED: List[str] = [
    'agent.tests.battery_test.run_battery_test',
    'agent.tests.battery_test.run_battery_discharge_test',
    'agent.tests.battery_test.run_battery_charge_test',
    'agent.tests.battery_test.run_battery_full_assessment'
]

RAM_TEST_PATHS: List[str] = [
    'agent.tests.visual_ram_test.run_visual_ram_test',
    'agent.tests.web_visual_ram_test.run_web_visual_ram_test',
    'agent.tests.ram_test.run_ram_test',
    'agent.tests.ram_test.run_advanced_ram_test'
]

# Visual tests that should be delegated to web session framework
VISUAL_TESTS: Set[str] = {
    'agent.tests.display_test.run_lcd_test_gui',
    'agent.tests.keyboard_test.run_keyboard_test',
    'agent.tests.pointing_device_test.run_pointing_device_test',
    'agent.tests.visual_cpu_test.visual_cpu_test',
    'agent.tests.visual_ram_test.run_visual_ram_test',
    'agent.tests.web_visual_ram_test.run_web_visual_ram_test',
    'agent.tests.touch_screen_test.run_touch_screen_test',
}

# Default test_kwargs for synchronous tests
SYNCHRONOUS_TEST_KWARGS: Dict[str, Dict[str, Any]] = {
    'agent.tests.cpu_test.run_basic_cpu_test': {},
    'agent.tests.ram_test.run_ram_test': {},
    'agent.tests.ram_test.run_advanced_ram_test': {},
    'agent.tests.cpu_test.run_cpu_stress_test': {},
}

# Platform-specific test availability constants
PLATFORM_SPECIFFS_WINDOWS = []
PLATFORM_SPECIFFS_LINUX = [
    'agent.tests.drive_wipe_test.run_secure_wipe_test'
]
PLATFORM_SPECIFFS_MAC = []

def get_platform_specific_tests() -> List[str]:
    """Get tests specific to the current platform."""
    system = platform.system()
    if system == 'Linux':
        return PLATFORM_SPECIFFS_LINUX
    elif system == 'Windows':
        return PLATFORM_SPECIFFS_WINDOWS
    elif system == 'Darwin':
        return PLATFORM_SPECIFFS_MAC
    return []

# Memory thresholds and configuration
DEFAULT_RAM_TEST_CONFIG = {
    'test_size_mode': 'percentage',
    'test_size_value': 25,
    'duration_seconds': 30
}

MEMORY_SAFETY_THRESHOLDS = {
    'conservative_limit': 0.25,  # 25% of available memory
    'aggressive_limit': 0.5,     # 50% of available memory
    'absolute_minimum_mb': 512   # 512MB absolute minimum
}

# Logging configuration
LOG_LEVELS = {
    'debug': 'DEBUG',
    'info': 'INFO',
    'warning': 'WARNING',
    'error': 'ERROR',
    'success': 'SUCCESS',
}

# Result status constants
RESULT_STATUSES = {
    'pass': 'PASS',
    'success': 'PASS',
    'fail': 'FAIL',
    'error': 'ERROR',
    'skipped': 'SKIPPED',
    'unknown': 'UNKNOWN'
}