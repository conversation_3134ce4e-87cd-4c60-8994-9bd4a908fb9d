"""
Unit tests for Crucible core modules.

This module provides comprehensive unit tests for all the new modular components
created during the refactoring process.
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from typing import Any, Dict
import sys
import os

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent.core.test_config import (
    RAM_TEST_PATHS, VISUAL_TESTS, RESULT_STATUSES,
    MEMORY_SAFETY_THRESHOLDS
)
from agent.core.logging_utils import (
    extract_status_and_notes_from_result, log_ram_test_configuration
)
from agent.core.performance_cache import (
    PerformanceCache, MemoryPool, TestOrchestratorPerformanceOptimizer,
    cached_function, get_performance_optimizer
)
from agent.core.import_manager import get_test_function


class TestConfigTests(unittest.TestCase):
    """Test the test_config module functions."""

    def test_ram_test_paths_constant(self):
        """Test that RAM_TEST_PATHS contains expected test paths."""
        self.assertIsInstance(RAM_TEST_PATHS, list)
        self.assertTrue(len(RAM_TEST_PATHS) > 0)
        self.assertTrue(all(isinstance(path, str) for path in RAM_TEST_PATHS))

    def test_visual_tests_constant(self):
        """Test that VISUAL_TESTS contains expected test paths."""
        self.assertIsInstance(VISUAL_TESTS, set)
        self.assertTrue(len(VISUAL_TESTS) > 0)
        self.assertTrue(all(isinstance(path, str) for path in VISUAL_TESTS))

    def test_memory_safety_thresholds(self):
        """Test memory safety thresholds are properly defined."""
        self.assertIsInstance(MEMORY_SAFETY_THRESHOLDS, dict)
        self.assertIn('conservative_limit', MEMORY_SAFETY_THRESHOLDS)
        self.assertIn('aggressive_limit', MEMORY_SAFETY_THRESHOLDS)
        self.assertIn('absolute_minimum_mb', MEMORY_SAFETY_THRESHOLDS)

        # Check that limits are reasonable
        self.assertGreater(MEMORY_SAFETY_THRESHOLDS['conservative_limit'], 0)
        self.assertGreater(MEMORY_SAFETY_THRESHOLDS['aggressive_limit'],
                          MEMORY_SAFETY_THRESHOLDS['conservative_limit'])


class TestLoggingUtils(unittest.TestCase):
    """Test the logging_utils module functions."""

    def test_extract_status_and_notes_from_result_dict(self):
        """Test status extraction from dictionary result."""
        result = {
            'test_details': {
                'status': 'PASS',
                'notes': 'Test completed successfully'
            }
        }
        status, notes = extract_status_and_notes_from_result(result)
        self.assertEqual(status, 'PASS')
        self.assertEqual(notes, 'Test completed successfully')

    def test_extract_status_and_notes_from_string(self):
        """Test status extraction from string result."""
        result = 'PASS'
        status, notes = extract_status_and_notes_from_result(result)
        self.assertEqual(status, 'PASS')
        self.assertEqual(notes, '')  # String inputs don't have notes, just status

    def test_extract_status_and_notes_from_unknown(self):
        """Test status extraction from unknown format."""
        result = {'unexpected': 'format'}
        status, notes = extract_status_and_notes_from_result(result)
        self.assertEqual(status, 'UNKNOWN')
        self.assertIsInstance(notes, str)

    def test_log_ram_test_configuration(self):
        """Test RAM test configuration logging."""
        mock_callback = Mock()
        ram_params = {
            'original_config': {
                'test_size_mode': 'percentage',
                'test_size_value': 25
            },
            'test_size_mb': 1024,
            'duration_seconds': 30,
            'config_source': 'profile'
        }

        log_ram_test_configuration({}, ram_params, 'test_name', mock_callback)
        mock_callback.assert_called_once()

        # Check that the log message contains expected elements
        call_args = mock_callback.call_args[0][0]
        self.assertIn('RAM test configuration', call_args)
        self.assertIn('25%', call_args)


class TestPerformanceCache(unittest.TestCase):
    """Test the performance cache functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.cache = PerformanceCache(default_ttl=1.0)  # Short TTL for testing

    def test_cache_set_and_get(self):
        """Test basic cache set and get operations."""
        self.cache.set('test_key', 'test_value')
        result = self.cache.get('test_key')
        self.assertEqual(result, 'test_value')

    def test_cache_expiration(self):
        """Test cache expiration functionality."""
        self.cache.set('test_key', 'test_value', ttl=0.1)
        time.sleep(0.2)
        result = self.cache.get('test_key')
        self.assertIsNone(result)

    def test_cache_cleanup(self):
        """Test cache cleanup functionality."""
        self.cache.set('test_key', 'test_value', ttl=0.1)
        time.sleep(0.2)
        self.cache.cleanup()
        self.assertEqual(len(self.cache.cache), 0)


class TestMemoryPool(unittest.TestCase):
    """Test the memory pool functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.pool = MemoryPool(max_pools=2)

    def test_memory_pool_get_buffer(self):
        """Test getting buffer from memory pool."""
        buffer = self.pool.get_buffer(1)  # Request 1MB
        self.assertIsInstance(buffer, bytearray)
        self.assertEqual(len(buffer), 1024 * 1024)

    def test_memory_pool_buffer_reuse(self):
        """Test buffer reuse functionality."""
        buffer1 = self.pool.get_buffer(1)
        self.pool.return_buffer(buffer1)

        buffer2 = self.pool.get_buffer(1)
        # Should return the same buffer (same address)
        self.assertEqual(buffer2, buffer1)


class TestPerformanceOptimizer(unittest.TestCase):
    """Test the performance optimizer functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.optimizer = TestOrchestratorPerformanceOptimizer()

    def test_get_cached_memory_info(self):
        """Test cached memory info retrieval."""
        info1 = self.optimizer.get_cached_memory_info()
        self.assertIsInstance(info1, dict)

        # Second call should return cached result quickly
        info2 = self.optimizer.get_cached_memory_info()
        self.assertEqual(info1, info2)

    def test_get_performance_metrics(self):
        """Test performance metrics retrieval."""
        metrics = self.optimizer.get_performance_metrics()
        self.assertIsInstance(metrics, dict)
        self.assertIn('cache_size', metrics)
        self.assertIn('pool_buffers', metrics)

    @patch('agent.core.performance_cache.psutil.virtual_memory')
    def test_cached_memory_check(self, mock_virtual_memory):
        """Test cached memory check function."""
        from agent.core.performance_cache import cached_memory_check

        # Mock psutil results
        mock_mem = Mock()
        mock_mem.available = 8 * 1024 * 1024 * 1024  # 8GB
        mock_mem.total = 16 * 1024 * 1024 * 1024     # 16GB
        mock_mem.used = 8 * 1024 * 1024 * 1024       # 8GB
        mock_mem.percent = 50.0
        mock_virtual_memory.return_value = mock_mem

        result = cached_memory_check(2048)  # Request 2GB
        self.assertIsInstance(result, dict)
        self.assertEqual(result['requested_mb'], 2048)
        self.assertGreater(result['safe_size_mb'], 0)


class TestImportManager(unittest.TestCase):
    """Test the import manager functionality."""

    def test_get_test_function_real_path(self):
        """Test getting a real test function by path."""
        # Test with a path that should fail gracefully (non-existent module)
        result = get_test_function('agent.tests.nonexistent_test.run_fake_test')
        self.assertIsNone(result)

    def test_get_test_function_format(self):
        """Test test function retrieval format."""
        # All our tests should return either None or a callable
        for path in RAM_TEST_PATHS[:3]:  # Test first 3 to avoid timeouts
            result = get_test_function(path)
            self.assertTrue(result is None or callable(result))


class TestCachedDecorator(unittest.TestCase):
    """Test the cached function decorator."""

    def test_cached_function_decorator(self):
        """Test that the cached decorator works properly."""

        @cached_function(ttl_seconds=1.0)
        def test_func(x):
            return x * 2

        result1 = test_func(5)
        result2 = test_func(5)  # Should return cached result
        self.assertEqual(result1, result2)
        self.assertEqual(result1, 10)

    def test_cached_function_expiration(self):
        """Test that cached function results expire."""

        @cached_function(ttl_seconds=0.1)
        def test_func(x):
            return time.time()

        result1 = test_func(1)
        time.sleep(0.2)
        result2 = test_func(1)  # Should compute new result
        self.assertNotEqual(result1, result2)


def run_all_tests():
    """Run all tests and return results."""
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromModule(sys.modules[__name__])

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    return result.wasSuccessful(), result.testsRun, len(result.failures), len(result.errors)


if __name__ == '__main__':
    run_all_tests()