"""
Import manager for cross-platform test module loading.

This module handles conditional imports for test modules based on platform availability
and provides a unified interface for test function access.
"""

import platform
from typing import Dict, Any, Optional, Callable
import sys


class TestImportManager:
    """Manages conditional imports of test modules."""

    def __init__(self):
        self._available_tests: Dict[str, Optional[Callable]] = {}
        self._initialize_imports()

    def _initialize_imports(self):
        """Initialize all conditional imports."""
        # Core test imports with fallbacks
        self._try_import('agent.tests.cpu_test', 'run_basic_cpu_test', None)

        # Try advanced modules first, then fallbacks
        try:
            from agent.tests.cpu_test import run_cpu_stress_test
            self._available_tests['run_cpu_stress_test'] = run_cpu_stress_test
        except ImportError:
            try:
                from agent.tests.visual_cpu_test import visual_cpu_test as run_cpu_stress_test
                self._available_tests['run_cpu_stress_test'] = run_cpu_stress_test
            except ImportError:
                self._available_tests['run_cpu_stress_test'] = None

        # RAM test imports with fallbacks
        try:
            from agent.tests.ram_test import run_ram_test, run_advanced_ram_test
            self._available_tests['run_ram_test'] = run_ram_test
            self._available_tests['run_advanced_ram_test'] = run_advanced_ram_test
        except ImportError:
            try:
                from agent.tests.visual_ram_test import run_visual_ram_test as ram_test
                self._available_tests['run_ram_test'] = ram_test
                self._available_tests['run_advanced_ram_test'] = ram_test
            except ImportError:
                self._available_tests['run_ram_test'] = None
                self._available_tests['run_advanced_ram_test'] = None

        # Platform-specific tests
        self._available_tests['run_secure_wipe_test'] = self._try_platform_import(
            'agent.tests.drive_wipe_test.run_secure_wipe_test',
            platforms=['Linux']
        )

        # Visual test imports
        try:
            from agent.tests.display_test import run_lcd_test_gui
            self._available_tests['run_lcd_test_gui'] = run_lcd_test_gui
        except ImportError:
            self._available_tests['run_lcd_test_gui'] = None

        try:
            from agent.tests.keyboard_test import run_keyboard_test
            self._available_tests['run_keyboard_test'] = run_keyboard_test
        except ImportError:
            self._available_tests['run_keyboard_test'] = None

        try:
            from agent.tests.pointing_device_test import run_pointing_device_test
            self._available_tests['run_pointing_device_test'] = run_pointing_device_test
        except ImportError:
            self._available_tests['run_pointing_device_test'] = None

        try:
            from agent.tests.visual_cpu_test import visual_cpu_test
            self._available_tests['visual_cpu_test'] = visual_cpu_test
        except ImportError:
            self._available_tests['visual_cpu_test'] = None

        try:
            from agent.tests.visual_ram_test import run_visual_ram_test
            self._available_tests['run_visual_ram_test'] = run_visual_ram_test
        except ImportError:
            self._available_tests['run_visual_ram_test'] = None

        try:
            from agent.tests.web_visual_ram_test import run_web_visual_ram_test
            self._available_tests['run_web_visual_ram_test'] = run_web_visual_ram_test
        except ImportError:
            self._available_tests['run_web_visual_ram_test'] = None

        try:
            from agent.tests.touch_screen_test import run_touch_screen_test
            self._available_tests['run_touch_screen_test'] = run_touch_screen_test
        except ImportError:
            self._available_tests['run_touch_screen_test'] = None

        # Battery tests (deprecated but may still exist)
        try:
            from agent.tests.battery_test import (
                run_battery_test, run_battery_discharge_test,
                run_battery_charge_test, run_battery_full_assessment
            )
            self._available_tests['run_battery_test'] = run_battery_test
            self._available_tests['run_battery_discharge_test'] = run_battery_discharge_test
            self._available_tests['run_battery_charge_test'] = run_battery_charge_test
            self._available_tests['run_battery_full_assessment'] = run_battery_full_assessment
        except ImportError:
            self._available_tests['run_battery_test'] = None
            self._available_tests['run_battery_discharge_test'] = None
            self._available_tests['run_battery_charge_test'] = None
            self._available_tests['run_battery_full_assessment'] = None

    def _try_import(self, module_name: str, func_name: str, fallback: Optional[Callable] = None) -> None:
        """Attempt to import a function from a module."""
        try:
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name, fallback)
            self._available_tests[func_name] = func
        except ImportError:
            self._available_tests[func_name] = fallback

    def _try_platform_import(self, import_str: str, platforms: list) -> Optional[Callable]:
        """Import only if current platform is in the allowed platforms."""
        current_platform = platform.system()
        if current_platform not in platforms:
            return None

        module_name, func_name = import_str.rsplit('.', 1)
        return self._import_from_path(module_name, func_name)

    def _import_from_path(self, module_name: str, func_name: str) -> Optional[Callable]:
        """Import a function from a module path."""
        try:
            module = __import__(module_name, fromlist=[func_name])
            return getattr(module, func_name)
        except (ImportError, AttributeError):
            return None

    def get_test_function(self, test_path: str) -> Optional[Callable]:
        """Get a test function by its path, handling module imports."""
        module_name, func_name = test_path.rsplit('.', 1)

        try:
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name, None)

            # Handle special cases for fallback implementations
            if func is None:
                # Lazy load alternate implementations
                if test_path == 'agent.tests.cpu_test.run_basic_cpu_test':
                    try:
                        from agent.tests.visual_cpu_test import visual_cpu_test as func
                    except ImportError:
                        func = None
                elif test_path in ['agent.tests.ram_test.run_ram_test', 'agent.tests.ram_test.run_advanced_ram_test']:
                    try:
                        from agent.tests.visual_ram_test import run_visual_ram_test as func
                    except ImportError:
                        try:
                            from agent.tests.web_visual_ram_test import run_web_visual_ram_test as func
                        except ImportError:
                            func = None

            return func

        except ImportError:
            return None

    def get_available_tests(self, test_paths: list) -> Dict[str, Optional[Callable]]:
        """Get available test functions for a list of test paths."""
        return {path: self.get_test_function(path) for path in test_paths}

    def is_visual_implementation(self, test_path: str, test_func: Callable) -> bool:
        """Check if a test function is the visual implementation."""
        if test_path == 'agent.tests.cpu_test.run_basic_cpu_test':
            try:
                from agent.tests.visual_cpu_test import visual_cpu_test
                return test_func is visual_cpu_test
            except ImportError:
                return False
        elif test_path in ['agent.tests.ram_test.run_ram_test', 'agent.tests.ram_test.run_advanced_ram_test']:
            try:
                from agent.tests.visual_ram_test import run_visual_ram_test
                from agent.tests.web_visual_ram_test import run_web_visual_ram_test
                return test_func is run_visual_ram_test or test_func is run_web_visual_ram_test
            except ImportError:
                return False
        return False


# Singleton instance
_import_manager = TestImportManager()

def get_test_function(test_path: str) -> Optional[Callable]:
    """Global function to get a test function by path."""
    return _import_manager.get_test_function(test_path)

def get_available_tests(test_paths: list) -> Dict[str, Optional[Callable]]:
    """Global function to get multiple test functions."""
    return _import_manager.get_available_tests(test_paths)