"""
Logging utilities for test execution and audit trails.

This module provides utilities for centralized logging, audit trail generation,
and summary reporting across different test types.
"""

from typing import Dict, Any, List, Callable
import psutil
from agent.core.test_config import RAM_TEST_PATHS, RESULT_STATUSES
from agent.tests.profiles import Profile


def extract_status_and_notes_from_result(result_data: Any) -> tuple[str, str]:
    """
    Extract status and notes from various result formats.

    Returns:
        Tuple of (status, notes)
    """
    status = 'unknown'
    notes = ''
    if isinstance(result_data, dict):
        if 'test_details' in result_data:
            raw_status = result_data.get('test_details', {}).get('status', 'unknown')
            status = str(raw_status).lower() if raw_status is not None else 'unknown'
            notes = result_data.get('test_details', {}).get('notes', '')
        elif 'status' in result_data:
            raw_status = result_data.get('status', 'unknown')
            status = str(raw_status).lower() if raw_status is not None else 'unknown'
            raw_notes = result_data.get('notes', '')
            if isinstance(raw_notes, list):
                notes = '; '.join(raw_notes)
            else:
                notes = str(raw_notes)
        else:
            notes = str(result_data)
            if len(notes) > 200:
                notes = notes[:200] + '...'
    elif isinstance(result_data, str):
        status = result_data.lower()
    else:
        status = 'error'
        notes = f'Unexpected result format: {type(result_data)}'
    return (RESULT_STATUSES.get(status, status.upper()), notes)


def log_ram_test_configuration(config: Dict[str, Any], ram_params: Dict[str, Any], test_name: str, log_callback: Callable):
    """Log RAM test configuration details."""
    config_info = ram_params['original_config']
    config_source = ram_params.get('config_source', 'unknown')

    if config_info.get('test_size_mode') == 'percentage':
        config_desc = f"{config_info.get('test_size_value', 25)}% ({ram_params['test_size_mb']} MB)"
    else:
        config_desc = f"{ram_params['test_size_mb']} MB"

    log_callback(
        f"RAM test configuration ({config_source}): {config_desc}, "
        f"{ram_params['duration_seconds']}s duration",
        'info'
    )

    # Log memory status for audit trail
    if 'memory_check' in ram_params:
        mem_check = ram_params['memory_check']
        log_callback(
            f"Memory status: {mem_check.get('available_mb', 0):.0f} MB available, "
            f"{mem_check.get('memory_pressure', 0):.1%} memory pressure",
            'debug'
        )


def log_ram_test_audit_trail(test_name: str, ram_params: Dict[str, Any], result: Dict[str, Any], log_callback: Callable):
    """Log comprehensive audit trail for RAM test execution."""
    try:
        # Extract configuration details
        config_info = ram_params.get('original_config', {})
        memory_check = ram_params.get('memory_check', {})
        config_source = ram_params.get('config_source', 'unknown')

        # Log configuration audit trail
        audit_info = [
            f"=== RAM Test Audit Trail: {test_name} ===",
            f"Configuration Source: {config_source}",
            f"Original Config Mode: {config_info.get('test_size_mode', 'unknown')}",
            f"Original Config Value: {config_info.get('test_size_value', 'unknown')}",
            f"Configured Duration: {config_info.get('duration_seconds', 'unknown')}s",
            f"Actual Test Size: {ram_params.get('test_size_mb', 'unknown')} MB",
            f"Actual Duration: {ram_params.get('duration_seconds', 'unknown')}s"
        ]

        # Add memory status information
        if memory_check:
            audit_info.extend([
                f"Available Memory: {memory_check.get('available_mb', 0):.0f} MB",
                f"Total Memory: {memory_check.get('total_mb', 0):.0f} MB",
                f"Memory Pressure: {memory_check.get('memory_pressure', 0):.1%}",
                f"Safety Adjusted: {memory_check.get('needs_adjustment', False)}",
                f"Adjustment Reason: {memory_check.get('adjustment_reason', 'none')}"
            ])

        # Add calculation metadata if available
        calc_metadata = ram_params.get('calculation_metadata', {})
        if calc_metadata:
            if calc_metadata.get('calculated_from_percentage'):
                audit_info.append(f"Percentage Calculation: {calc_metadata.get('requested_percentage', 0)}% of {calc_metadata.get('available_memory_mb', 0):.0f} MB")
            if calc_metadata.get('safety_adjusted'):
                audit_info.append(f"Profile Safety Adjustment: Yes")
            if 'error' in calc_metadata:
                audit_info.append(f"Calculation Error: {calc_metadata['error']}")

        # Add test result information
        if isinstance(result, dict):
            if 'test_details' in result:
                test_details = result['test_details']
                audit_info.extend([
                    f"Test Status: {test_details.get('status', 'unknown')}",
                    f"Test Notes: {test_details.get('notes', 'none')}"
                ])
                if 'error_type' in test_details:
                    audit_info.append(f"Error Type: {test_details['error_type']}")

        audit_info.append("=== End Audit Trail ===")

        # Log each line of the audit trail
        for line in audit_info:
            log_callback(line, 'info')

    except Exception as e:
        log_callback(f"Error logging RAM test audit trail: {str(e)}", 'error')


def log_ram_test_summary(profile: Profile, log_callback: Callable):
    """Log a summary of all RAM test configurations used in this test session."""
    try:
        if not profile:
            return

        # Find RAM tests that were configured or executed
        ram_tests_in_profile = [test for test in profile.tests if test in RAM_TEST_PATHS]

        if not ram_tests_in_profile:
            return

        log_callback("=== RAM Test Configuration Summary ===", 'info')
        log_callback(f"Profile: {profile.name} ({profile.device_type})", 'info')

        # Get system memory info for context
        try:
            mem_info = psutil.virtual_memory()
            total_gb = mem_info.total / (1024 * 1024 * 1024)
            available_gb = mem_info.available / (1024 * 1024 * 1024)
            log_callback(f"System Memory: {total_gb:.1f} GB total, {available_gb:.1f} GB available", 'info')
        except:
            log_callback("System Memory: Unable to determine", 'info')

        # Log configuration for each RAM test
        for test_path in ram_tests_in_profile:
            test_name = test_path.split('.')[-1].replace('run_', '').replace('_test', ' Test').title()
            config = profile.get_ram_test_config(test_path)

            config_source = "profile" if test_path in profile.test_args else "default"
            mode = config.get('test_size_mode', 'percentage')
            value = config.get('test_size_value', 25)
            duration = config.get('duration_seconds', 30)

            if mode == 'percentage':
                config_desc = f"{value}% of available memory"
            else:
                config_desc = f"{value} MB absolute"

            log_callback(
                f"  {test_name}: {config_desc}, {duration}s duration (source: {config_source})",
                'info'
            )

        log_callback("=== End RAM Test Summary ===", 'info')

    except Exception as e:
        log_callback(f"Error logging RAM test summary: {str(e)}", 'error')


def log_final_summary_data(end_screen_summary_data: Dict, log_callback: Callable, final_log_level: str = None):
    """Log final test completion summary."""
    if final_log_level is None:
        final_log_level = 'success' if end_screen_summary_data['overall_status'] == 'PASS' else 'error'

    log_callback(f"All tests completed. Overall Status: {end_screen_summary_data['overall_status']}", final_log_level)

    if end_screen_summary_data['errors']:
        log_callback(f"Encountered {len(end_screen_summary_data['errors'])} error(s)/issue(s) during testing:", 'warning')
        for err_entry in end_screen_summary_data['errors']:
            log_callback(f' - {err_entry}', 'warning')