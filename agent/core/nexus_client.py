"""
Centralized Nexus API client for reliable communication between Crucible and Nexus.
This module provides a clean interface for all Nexus operations with proper error handling,
retry logic, and consistent operator ID transmission.
"""

import os
import json
import time
import uuid
import logging
import requests
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

try:
    import psutil
except ImportError:
    psutil = None


@dataclass
class NexusConfig:
    """Configuration for Nexus API client."""
    base_url: str
    timeout: int = 10
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class TestResult:
    """Standardized test result structure for Nexus submission."""
    asset_number: str
    operator_id: str
    test_name: str
    status: str
    profile_name: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    started_at: Optional[str] = None
    ended_at: Optional[str] = None
    mac: Optional[str] = None
    run_id: Optional[str] = None
    category: Optional[str] = None


class NexusClient:
    """Centralized client for all Nexus API operations."""
    
    def __init__(self, config: Optional[NexusConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or self._load_default_config()
        self._session = requests.Session()
        self._session.headers.update({'Content-Type': 'application/json'})
    
    def _load_default_config(self) -> NexusConfig:
        """Load default configuration from environment variables."""
        base_url = os.environ.get('NEXUS_BASE_URL', 'http://localhost:8080').rstrip('/')
        timeout = int(os.environ.get('NEXUS_TIMEOUT', '10'))
        max_retries = int(os.environ.get('NEXUS_MAX_RETRIES', '3'))
        retry_delay = float(os.environ.get('NEXUS_RETRY_DELAY', '1.0'))
        
        return NexusConfig(
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay
        )
    
    def _resolve_mac_address(self) -> Optional[str]:
        """Auto-detect MAC address with fallback logic."""
        # Try environment variable first
        mac = os.environ.get('NEXUS_MAC')
        if mac:
            return mac
        
        # Try psutil detection
        if psutil is not None:
            try:
                for iface, addrs in psutil.net_if_addrs().items():
                    # Skip virtual/loopback interfaces
                    if iface.lower().startswith(('lo', 'docker', 'veth', 'vmnet', 'virbr')):
                        continue
                    for addr in addrs:
                        if (getattr(addr, 'family', None) == getattr(getattr(psutil, 'AF_LINK', None), 'value', None) or 
                            getattr(addr, 'family', None) == 17):
                            mac_candidate = getattr(addr, 'address', None)
                            if mac_candidate and len(mac_candidate) >= 11 and mac_candidate != '00:00:00:00:00:00':
                                return mac_candidate
            except Exception as e:
                self.logger.debug(f"psutil MAC detection failed: {e}")
        
        # Fallback to uuid.getnode()
        try:
            node = uuid.getnode()
            if node:
                return ':'.join(f"{(node >> ele) & 0xff:02x}" for ele in range(40, -1, -8))
        except Exception as e:
            self.logger.debug(f"uuid.getnode() MAC detection failed: {e}")
        
        return None
    
    def _normalize_status(self, status: str) -> str:
        """Normalize status to Nexus-compatible values."""
        status_map = {
            'pass': 'passed',
            'passed': 'passed',
            'fail': 'failed',
            'failed': 'failed',
            'error': 'failed',
            'warning': 'skipped',
            'skip': 'skipped',
            'skipped': 'skipped'
        }
        
        normalized = status_map.get(str(status).lower())
        if normalized:
            return normalized
        
        # Default fallback
        if str(status).lower() in ('unknown', ''):
            return 'failed'
        
        return str(status).lower()
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> requests.Response:
        """Make HTTP request with retry logic."""
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                if method.upper() == 'POST':
                    response = self._session.post(
                        url, 
                        json=data, 
                        timeout=self.config.timeout
                    )
                elif method.upper() == 'GET':
                    response = self._session.get(
                        url, 
                        params=data, 
                        timeout=self.config.timeout
                    )
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                # Log the request for debugging
                self.logger.debug(f"Nexus {method} {url} -> {response.status_code}")
                if data and 'operator' in str(data):
                    self.logger.info(f"Nexus request with operator: {data.get('operator', 'N/A')}")
                
                return response
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Nexus request attempt {attempt + 1} failed: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                else:
                    raise
        
        raise Exception("All retry attempts exhausted")
    
    def submit_test_result(self, result: TestResult) -> bool:
        """Submit a test result to Nexus with robust operator ID handling."""
        try:
            # Ensure operator ID is not empty
            effective_operator = result.operator_id or os.environ.get('NEXUS_OPERATOR')
            if not effective_operator:
                self.logger.warning(f"No operator ID provided for test result: {result.test_name}")
            
            # Build payload with multiple operator field names for maximum compatibility
            payload = {
                'asset_number': result.asset_number,
                'mac': result.mac or self._resolve_mac_address(),
                'profile_name': result.profile_name or os.environ.get('NEXUS_PROFILE_NAME'),
                'run_id': result.run_id,
                'category': result.category,
                'test_name': result.test_name,
                'status': self._normalize_status(result.status),
                'metrics': result.metrics,
                'operator': effective_operator,  # Primary field name
                'operator_id': effective_operator,  # Fallback field name
                'operatorId': effective_operator,  # Alternative field name
                'started_at': result.started_at,
                'ended_at': result.ended_at,
            }
            
            # Remove None values to keep payload clean
            payload = {k: v for k, v in payload.items() if v is not None}
            
            # Log the submission for debugging
            self.logger.info(
                f"Submitting to Nexus: asset={result.asset_number}, "
                f"operator={effective_operator}, test={result.test_name}, "
                f"status={payload['status']}"
            )
            
            response = self._make_request('POST', '/api/results', payload)
            
            if response.status_code in (200, 201):
                self.logger.info(f"Successfully submitted test result to Nexus: {result.test_name}")
                return True
            else:
                self.logger.error(
                    f"Nexus submission failed: {response.status_code} - {response.text}"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to submit test result to Nexus: {e}")
            return False
    
    def submit_batch_results(self, results: List[TestResult]) -> bool:
        """Submit multiple test results in a single batch."""
        try:
            batch_payload = {
                'results': []
            }
            
            for result in results:
                effective_operator = result.operator_id or os.environ.get('NEXUS_OPERATOR')
                
                result_payload = {
                    'asset_number': result.asset_number,
                    'mac': result.mac or self._resolve_mac_address(),
                    'profile_name': result.profile_name or os.environ.get('NEXUS_PROFILE_NAME'),
                    'run_id': result.run_id,
                    'category': result.category,
                    'test_name': result.test_name,
                    'status': self._normalize_status(result.status),
                    'metrics': result.metrics,
                    'operator': effective_operator,
                    'operator_id': effective_operator,
                    'operatorId': effective_operator,
                    'started_at': result.started_at,
                    'ended_at': result.ended_at,
                }
                
                # Remove None values
                result_payload = {k: v for k, v in result_payload.items() if v is not None}
                batch_payload['results'].append(result_payload)
            
            self.logger.info(f"Submitting batch of {len(results)} results to Nexus")
            
            response = self._make_request('POST', '/api/results/batch', batch_payload)
            
            if response.status_code in (200, 201):
                self.logger.info(f"Successfully submitted batch results to Nexus")
                return True
            else:
                self.logger.error(
                    f"Nexus batch submission failed: {response.status_code} - {response.text}"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to submit batch results to Nexus: {e}")
            return False
    
    def get_results(self, asset_number: Optional[str] = None, 
                   operator: Optional[str] = None, **kwargs) -> List[Dict]:
        """Retrieve results from Nexus with filtering options."""
        try:
            params = {}
            if asset_number:
                params['asset'] = asset_number
            if operator:
                params['operator'] = operator
            
            # Add any additional filter parameters
            params.update(kwargs)
            
            response = self._make_request('GET', '/api/results', params)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to retrieve results: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to retrieve results from Nexus: {e}")
            return []
    
    def check_connectivity(self) -> bool:
        """Check if Nexus is reachable."""
        try:
            response = self._make_request('GET', '/api/results', {'limit': 1})
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Nexus connectivity check failed: {e}")
            return False


# Global instance for easy access
_nexus_client = None

def get_nexus_client() -> NexusClient:
    """Get the global Nexus client instance."""
    global _nexus_client
    if _nexus_client is None:
        _nexus_client = NexusClient()
    return _nexus_client


def submit_test_result_to_nexus(asset_number: str, operator_id: str, test_name: str, 
                               result_data: Dict[str, Any], **kwargs) -> bool:
    """Convenience function for submitting a single test result."""
    client = get_nexus_client()
    
    # Extract status from result_data
    status = 'unknown'
    if 'test_details' in result_data:
        status = result_data.get('test_details', {}).get('status', 'unknown')
    elif 'status' in result_data:
        status = result_data.get('status', 'unknown')
    
    result = TestResult(
        asset_number=asset_number,
        operator_id=operator_id,
        test_name=test_name,
        status=status,
        metrics=result_data.get('metrics', result_data),
        started_at=result_data.get('started_at'),
        ended_at=result_data.get('finished_at') or result_data.get('ended_at'),
        **kwargs
    )
    
    return client.submit_test_result(result)
