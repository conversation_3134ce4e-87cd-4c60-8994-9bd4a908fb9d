"""
Performance optimization utilities for the Crucible test orchestrator.

This module provides caching, pooling, and optimization utilities to improve
test execution performance and reduce memory overhead.
"""

import time
import psutil
from typing import Dict, Any, Optional, Callable, List
from functools import lru_cache, wraps
from dataclasses import dataclass, field
from concurrent.futures import Thread<PERSON>oolExecutor
from agent.core.logging_utils import log_final_summary_data


@dataclass
class CacheEntry:
    """Cache entry with timestamp and TTL support."""
    data: Any
    created_at: float
    ttl: Optional[float] = None

    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl


class MemoryPool:
    """Simple memory buffer pool for RAM tests."""

    def __init__(self, max_pools: int = 3):
        self.pools: List[bytearray] = []
        self.max_pools = max_pools
        self.pool_sizes = [50 * 1024 * 1024, 100 * 1024 * 1024, 512 * 1024 * 1024]  # MB allocations

    def get_buffer(self, size_mb: int) -> Optional[bytearray]:
        """Get a buffer from the pool or create a new one."""
        size_bytes = size_mb * 1024 * 1024

        # Try to find an existing buffer of appropriate size
        for i, pool in enumerate(self.pools):
            if len(pool) >= size_bytes:
                self.pools.pop(i)  # Remove from pool for exclusive use
                return pool

        # Create new buffer if pool limit not exceeded
        if len(self.pools) < self.max_pools:
            try:
                buffer = bytearray(size_bytes)
                return buffer
            except MemoryError:
                return None

        return None

    def return_buffer(self, buffer: bytearray):
        """Return a buffer to the pool for reuse."""
        if len(self.pools) < self.max_pools:
            # Reallocate to standard sizes for reuse
            for size in self.pool_sizes:
                if len(buffer) <= size:
                    # Resize to standard size for better reuse
                    buffer.clear()
                    buffer.extend(bytearray(size))
                    self.pools.append(buffer)
                    break
        else:
            # Pool full, buffer will be garbage collected
            pass


class PerformanceCache:
    """Main performance cache with TTL and cleanup."""

    def __init__(self, default_ttl: float = 300.0):  # 5 minute default
        self.cache: Dict[str, CacheEntry] = {}
        self.default_ttl = default_ttl
        self.last_cleanup = time.time()

    def get(self, key: str) -> Any:
        """Get cached value if not expired."""
        if key in self.cache:
            entry = self.cache[key]
            if not entry.is_expired():
                return entry.data
            else:
                del self.cache[key]
        return None

    def set(self, key: str, data: Any, ttl: Optional[float] = None):
        """Set cached value with optional TTL."""
        effective_ttl = ttl if ttl is not None else self.default_ttl
        self.cache[key] = CacheEntry(data, time.time(), effective_ttl)

    def cleanup(self):
        """Remove expired entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry.is_expired()
        ]
        for key in expired_keys:
            del self.cache[key]
        self.last_cleanup = current_time

    def clear(self, pattern: str = None):
        """Clear cache entries matching pattern or all entries."""
        if pattern:
            keys_to_remove = [k for k in self.cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self.cache[key]
        else:
            self.cache.clear()


class TestOrchestratorPerformanceOptimizer:
    """Performance optimizer for the test orchestrator."""

    def __init__(self):
        self.cache = PerformanceCache()
        self.memory_pool = MemoryPool()
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="crucible-test")
        self.handler_cache: Dict[str, Any] = {}  # Cache handler instances

    def get_cached_memory_info(self, force_refresh: bool = False) -> Dict[str, float]:
        """Get cached system memory information."""
        cache_key = "system_memory"

        if not force_refresh:
            cached = self.cache.get(cache_key)
            if cached:
                return cached

        # Get fresh memory info
        mem_info = psutil.virtual_memory()
        memory_data = {
            'total': mem_info.total,
            'available': mem_info.available,
            'used': mem_info.used,
            'percent': mem_info.percent
        }

        self.cache.set(cache_key, memory_data, ttl=30.0)  # Cache for 30 seconds
        return memory_data

    def get_cached_profile_config(self, profile_name: str, force_refresh: bool = False) -> Dict[str, Any]:
        """Cache profile configurations to avoid repeated file I/O."""
        cache_key = f"profile_config_{profile_name}"

        if not force_refresh:
            cached = self.cache.get(cache_key)
            if cached:
                return cached

        # This would normally load from file - for now return empty dict
        # In real implementation, this would load the profile from disk
        profile_data = {}
        self.cache.set(cache_key, profile_data, ttl=60.0)  # Cache for 1 minute
        return profile_data

    def get_reusable_handler(self, handler_type: str, *args, **kwargs) -> Any:
        """Get a cached handler instance for reuse."""
        cache_key = f"handler_{handler_type}_{hash(str(args) + str(kwargs))}"

        if cache_key in self.handler_cache:
            return self.handler_cache[cache_key]

        # Create new handler - this will be plugged in when we implement more handlers
        handler = None  # Placeholder
        if handler:
            self.handler_cache[cache_key] = handler

        return handler

    def get_memory_buffer(self, size_mb: int) -> Optional[bytearray]:
        """Get memory buffer from pool."""
        return self.memory_pool.get_buffer(size_mb)

    def return_memory_buffer(self, buffer: bytearray):
        """Return memory buffer to pool."""
        self.memory_pool.return_buffer(buffer)

    def schedule_async_cleanup(self):
        """Schedule async cache cleanup."""
        def cleanup_task():
            self.cache.cleanup()

        self.executor.submit(cleanup_task)

    def optimize_ram_test_execution(self, test_size_mb: int, memory_info: Dict[str, float]) -> Dict[str, Any]:
        """Optimize RAM test execution parameters based on system state."""
        available_mb = memory_info['available'] / (1024 * 1024)  # Convert to MB

        # Apply performance-aware safety limits
        aggressive_limit = min(available_mb * 0.4, test_size_mb * 1.5)  # Up to 40% available or 1.5x requested
        conservative_limit = min(available_mb * 0.2, test_size_mb)       # Conservative limit

        return {
            'optimized_test_size_mb': min(test_size_mb, conservative_limit),
            'aggressive_limit': aggressive_limit,
            'available_mb': available_mb,
            'memory_efficiency': available_mb / (available_mb + memory_info['used'] / (1024 * 1024)) if memory_info['used'] > 0 else 1.0
        }

    def prewarm_handlers(self, test_paths: List[str]):
        """Pre-warm handler instances for frequently used tests."""
        for test_path in test_paths[:3]:  # Only pre-warm the first 3 for performance
            try:
                from agent.core.import_manager import get_test_function
                func = get_test_function(test_path)
                if func:
                    cache_key = f"handler_function_{test_path}"
                    self.cache.set(cache_key, func, ttl=600.0)  # Cache for 10 minutes
            except Exception:
                pass

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        return {
            'cache_size': len(self.cache.cache),
            'pool_buffers': len(self.memory_pool.pools),
            'handler_cache_size': len(self.handler_cache),
            'active_threads': len([t for t in self.executor._threads if t.is_alive()]),
            'memory_usage': self.get_cached_memory_info()
        }

    def reset(self):
        """Reset all caches and pools."""
        self.cache.clear()
        self.handler_cache.clear()
        self.memory_pool = MemoryPool()


# Global performance optimizer instance
_performance_optimizer = TestOrchestratorPerformanceOptimizer()

def get_performance_optimizer() -> TestOrchestratorPerformanceOptimizer:
    """Get the global performance optimizer instance."""
    global _performance_optimizer
    return _performance_optimizer

def initialize_performance_optimizer():
    """Initialize the performance optimizer and do any pre-warming."""
    optimizer = get_performance_optimizer()

    # Pre-warm with common test patterns
    common_tests = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.visual_cpu_test.visual_cpu_test',
        'agent.tests.display_test.run_lcd_test_gui'
    ]
    optimizer.prewarm_handlers(common_tests)

    # Get initial memory info to populate cache
    optimizer.get_cached_memory_info()

def get_performance_metrics() -> Dict[str, Any]:
    """Get comprehensive performance metrics."""
    return get_performance_optimizer().get_performance_metrics()

def cached_function(ttl_seconds: float = 300.0):
    """Decorator to cache function results."""
    def decorator(func):
        cache = {}

        @wraps(func)
        def wrapper(*args, **kwargs):
            key = str(args) + str(sorted(kwargs.items()))

            # Check if result is cached and not expired
            if key in cache:
                result, created_at = cache[key]
                if time.time() - created_at < ttl_seconds:
                    return result

            # Compute and cache result
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())

            return result

        return wrapper
    return decorator

@lru_cache(maxsize=100)
def cached_memory_check(requested_mb: int) -> Dict[str, Any]:
    """Cached memory availability check to avoid repeated psutil calls."""
    try:
        mem_info = psutil.virtual_memory()
        available_mb = mem_info.available / (1024 * 1024)
        total_mb = mem_info.total / (1024 * 1024)
        used_mb = mem_info.used / (1024 * 1024)

        performance_data = get_performance_optimizer().optimize_ram_test_execution(
            requested_mb,
            {'total': total_mb, 'available': available_mb, 'used': used_mb, 'percent': mem_info.percent}
        )

        from agent.core.test_config import MEMORY_SAFETY_THRESHOLDS

        conservative_limit_mb = int(available_mb * MEMORY_SAFETY_THRESHOLDS['conservative_limit'])
        aggressive_limit_mb = int(available_mb * MEMORY_SAFETY_THRESHOLDS['aggressive_limit'])
        absolute_minimum_mb = MEMORY_SAFETY_THRESHOLDS['absolute_minimum_mb']

        needs_adjustment = requested_mb > conservative_limit_mb
        is_dangerous = requested_mb > aggressive_limit_mb

        if is_dangerous:
            safe_size_mb = conservative_limit_mb
            adjustment_reason = "exceeds safe memory limit"
        elif needs_adjustment:
            safe_size_mb = min(requested_mb, aggressive_limit_mb)
            adjustment_reason = "adjusted to conservative limit"
        else:
            safe_size_mb = max(requested_mb, absolute_minimum_mb)
            adjustment_reason = "no adjustment needed"

        return {
            "requested_mb": requested_mb,
            "safe_size_mb": safe_size_mb,
            "available_mb": available_mb,
            "total_mb": total_mb,
            "used_mb": used_mb,
            "needs_adjustment": needs_adjustment,
            "is_dangerous": is_dangerous,
            "adjustment_reason": adjustment_reason,
            "memory_pressure": used_mb / total_mb if total_mb > 0 else 1.0,
            "conservative_limit_mb": conservative_limit_mb,
            "aggressive_limit_mb": aggressive_limit_mb,
            "performance_metrics": performance_data,
            "cached_at": time.time()
        }

    except Exception as e:
        return {
            "requested_mb": requested_mb,
            "safe_size_mb": 1024,
            "available_mb": 0,
            "total_mb": 0,
            "used_mb": 0,
            "needs_adjustment": True,
            "is_dangerous": True,
            "adjustment_reason": f"memory check failed: {str(e)}",
            "memory_pressure": 1.0,
            "conservative_limit_mb": 1024,
            "aggressive_limit_mb": 1024,
            "error": str(e)
        }