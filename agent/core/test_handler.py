"""
Base classes for test handlers in the Crucible test orchestrator.

This module provides the abstract base class and specialized handlers
for different types of hardware tests (synchronous, visual, RAM-based).
"""

from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, Optional
from agent.core.result_manager import ResultManager
from agent.tests.profiles import Profile
from agent.core.test_config import RESULT_STATUSES
from agent.core.logging_utils import log_ram_test_configuration, log_ram_test_audit_trail
from agent.core.performance_cache import cached_memory_check
from agent.tests.profiles import calculate_ram_test_size


class TestResult:
    """Standardized result format for test execution."""

    def __init__(self, status: str, notes: str = "", error_type: Optional[str] = None, **metadata):
        self.status = RESULT_STATUSES.get(status.lower(), 'UNKNOWN')
        self.notes = notes
        self.error_type = error_type
        self.metadata = metadata

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for result manager."""
        result = {
            "test_details": {
                "status": self.status,
                "notes": self.notes
            }
        }

        if self.error_type:
            result["test_details"]["error_type"] = self.error_type

        if self.metadata:
            result["test_details"].update(self.metadata)

        return result

    def to_simple_dict(self) -> Dict[str, Any]:
        """Simplified format for summary data."""
        return {
            'status': self.status.upper(),
            'notes': self.notes
        }


class TestHandler(ABC):
    """Abstract base class for test handlers."""

    def __init__(self, test_path: str, profile: Profile,
                 log_callback: Callable[[str, str], None],
                 result_manager: ResultManager,
                 asset_number_callback: Callable[[], str],
                 operator_id_callback: Callable[[], str]):
        self.test_path = test_path
        self.profile = profile
        self.log_callback = log_callback
        self.result_manager = result_manager
        self.asset_number_callback = asset_number_callback
        self.operator_id_callback = operator_id_callback
        self.test_name = self._extract_test_name(test_path)

    def _extract_test_name(self, test_path: str) -> str:
        """Extract human-readable test name from path."""
        return test_path.split('.')[-1].replace('run_', '').replace('_test', '').title()

    @abstractmethod
    def execute(self, **kwargs) -> TestResult:
        """Execute the test and return a TestResult."""
        pass

    def log(self, message: str, level: str = 'info'):
        """Log a message using the configured callback."""
        self.log_callback(message, level)

    def add_result(self, result: TestResult):
        """Add result to the result manager."""
        self.result_manager.add_result(
            self.asset_number_callback(),
            self.operator_id_callback(),
            self.test_name,
            result.to_dict(),
            profile_name=self.profile.name if self.profile else None
        )


class SynchronousTestHandler(TestHandler):
    """Handler for synchronous (non-visual) tests."""

    def __init__(self, test_path: str, test_function: Callable,
                 profile: Profile, log_callback: Callable[[str, str], None],
                 result_manager: ResultManager,
                 asset_number_callback: Callable[[], str],
                 operator_id_callback: Callable[[], str]):
        super().__init__(test_path, profile, log_callback, result_manager,
                        asset_number_callback, operator_id_callback)
        self.test_function = test_function

    def execute(self, **kwargs) -> TestResult:
        """Execute the synchronous test."""
        self.log(f'Running {self.test_name}...', 'info')

        try:
            result = self.test_function(**kwargs)

            # Handle various result formats uniformly
            if result is None:
                return TestResult('FAIL', f'{self.test_name} returned None result')
            elif isinstance(result, dict):
                # Extract status and notes from result
                status = self._extract_status_from_dict(result)
                notes = self._extract_notes_from_dict(result)
                return TestResult(status, notes, raw_result=result)
            elif isinstance(result, str):
                return TestResult(result.lower(), f'{self.test_name} result: {result}')
            else:
                return TestResult('PASS', f'{self.test_name} completed', raw_result=result)

        except Exception as e:
            error_msg = f'Error running {self.test_name}: {str(e)}'
            self.log(error_msg, 'error')
            return TestResult('FAIL', error_msg, type(e).__name__)

    def _extract_status_from_dict(self, result: Dict) -> str:
        """Extract status from dictionary result."""
        if 'test_details' in result and 'status' in result['test_details']:
            return result['test_details']['status']
        elif 'status' in result:
            return result['status']
        return 'PASS'

    def _extract_notes_from_dict(self, result: Dict) -> str:
        """Extract notes from dictionary result."""
        if 'test_details' in result and 'notes' in result['test_details']:
            return result['test_details']['notes']
        elif 'notes' in result:
            notes = result['notes']
            return '; '.join(notes) if isinstance(notes, list) else str(notes)
        elif 'details' in result:
            notes = result['details']
            return '; '.join(notes) if isinstance(notes, list) else str(notes)
        return ''


class VisualTestHandler(TestHandler):
    """Handler for visual tests that delegate to web sessions."""

    def execute(self, **kwargs) -> TestResult:
        """Mark visual test as delegated."""
        note = 'Delegated to web visual test session; execution handled asynchronously.'
        self.log(f'Delegating visual test {self.test_name} to web session.', 'info')

        # For LCD test specifically, we should not mark as SKIPPED if it's meant to complete
        # Instead, let the actual completion happen through web session
        if 'run_lcd_test_gui' in self.test_path:
            note = 'LCD Test initiated. Waiting for technician verification completion.'
            # Don't return SKIPPED - let the session complete with actual result
            return TestResult('UNKNOWN', note, await_completion=True)

        return TestResult('SKIPPED', note)


class RamTestHandler(SynchronousTestHandler):
    """Specialized handler for RAM tests with memory safety and audit trails."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Fully modularized - uses direct imports and method calls

    def execute(self, **kwargs) -> TestResult:
        """Execute RAM test with safety checks and audit logging."""
        try:
            # Resolve RAM test parameters (now using local method)
            ram_params = self.resolve_ram_test_parameters()

            # Update kwargs with resolved parameters
            test_kwargs = kwargs.copy()
            test_kwargs.update({
                'test_size_mb': ram_params['test_size_mb'],
                'duration_seconds': ram_params['duration_seconds']
            })

            # Log configuration
            log_ram_test_configuration(ram_params, self.test_name, self.log)

            # Execute with safety wrapper (now using local method)
            result_dict = self.safe_ram_test_execution(ram_params, **test_kwargs)

            # Add comprehensive audit data
            if isinstance(result_dict, dict) and ram_params:
                self._add_audit_metadata(result_dict, ram_params)

            # Log audit trail using local method
            if ram_params:
                RamTestHandler.log_ram_test_audit_trail(self.test_name, ram_params, result_dict, self.log)

            # Convert to TestResult
            status = self._extract_status_from_dict(result_dict)
            notes = self._extract_notes_from_dict(result_dict)

            return TestResult(status, notes, **result_dict)

        except Exception as e:
            error_msg = f'Critical error in RAM test {self.test_name}: {str(e)}'
            self.log(error_msg, 'error')
            return TestResult('FAIL', error_msg, type(e).__name__)

    def _add_audit_metadata(self, result_dict: Dict, ram_params: Dict):
        """Add comprehensive audit metadata to result."""
        audit_data = {
            'configured_mb': ram_params.get('test_size_mb'),
            'duration_seconds': ram_params.get('duration_seconds'),
            'config_source': ram_params.get('config_source'),
            'original_config': ram_params.get('original_config'),
            'safety_adjusted': ram_params.get('memory_check', {}).get('needs_adjustment', False),
            'memory_status': ram_params.get('memory_check', {}),
            'calculation_metadata': ram_params.get('calculation_metadata', {}),
            'test_timestamp': __import__('time').time()
        }

        if 'test_details' in result_dict:
            result_dict['test_details']['ram_config'] = audit_data
        else:
            result_dict['ram_config'] = audit_data

        result_dict['test_type'] = 'ram_test'
        result_dict['test_path'] = self.test_path
        result_dict['profile_name'] = self.profile.name if self.profile else 'unknown'

    def resolve_ram_test_parameters(self):
        """
        Resolve RAM test parameters from profile configuration with comprehensive safety checks.
        """
        try:
            # Get RAM test configuration from profile
            config = self.profile.get_ram_test_config(self.test_path)

            # Calculate actual test size using the utility function
            calculation_result = calculate_ram_test_size(config)

            # Perform additional memory availability check
            memory_check = self.check_memory_availability(calculation_result["test_size_mb"])

            # Use the safer of the two calculations
            final_size_mb = memory_check["safe_size_mb"]

            # Extract resolved parameters
            resolved_params = {
                "test_size_mb": final_size_mb,
                "duration_seconds": config.get("duration_seconds", 30),
                "original_config": calculation_result["original_config"],
                "calculation_metadata": calculation_result,
                "memory_check": memory_check,
                "config_source": "profile" if self.test_path in self.profile.test_args else "default"
            }

            # Log parameter resolution details
            if calculation_result.get("safety_adjusted", False) or memory_check["needs_adjustment"]:
                if calculation_result.get("calculated_from_percentage", False):
                    original_calc_mb = int(calculation_result.get('available_memory_mb', 0) * calculation_result.get('requested_percentage', 0) / 100)
                    self.log(
                        f"RAM test size adjusted for safety: requested {calculation_result.get('requested_percentage', 0)}% "
                        f"({original_calc_mb} MB), using {final_size_mb} MB instead "
                        f"({memory_check['adjustment_reason']})",
                        'warning'
                    )
                else:
                    self.log(
                        f"RAM test size adjusted for safety: requested {calculation_result.get('requested_mb', 0)} MB, "
                        f"using {final_size_mb} MB instead ({memory_check['adjustment_reason']})",
                        'warning'
                    )

            # Log memory pressure warning if high
            if memory_check["memory_pressure"] > 0.8:
                self.log(
                    f"High memory pressure detected ({memory_check['memory_pressure']:.1%} used). "
                    f"RAM test size limited to {final_size_mb} MB for system stability",
                    'warning'
                )

            return resolved_params

        except Exception as e:
            # Log error and return safe defaults
            self.log(f"Error resolving RAM test parameters for {self.test_path}: {str(e)}", 'error')

            # Return safe default parameters with error handling
            try:
                from agent.core.test_config import MEMORY_SAFETY_THRESHOLDS
                safe_size_mb = max(512, int(8000 * 0.1))  # 10% of 8GB available or 512MB minimum
            except:
                safe_size_mb = 512  # 512MB fallback

            return {
                "test_size_mb": safe_size_mb,
                "duration_seconds": 30,
                "original_config": {"test_size_mode": "percentage", "test_size_value": 25, "duration_seconds": 30},
                "calculation_metadata": {"error": str(e), "safety_adjusted": True},
                "memory_check": {"error": str(e), "safe_size_mb": safe_size_mb},
                "config_source": "error_fallback"
            }

    def check_memory_availability(self, requested_mb: int):
        """Check memory availability using cached approach."""
        return cached_memory_check(requested_mb)

    def safe_ram_test_execution(self, ram_params, **kwargs):
        """
        Safely execute RAM test with comprehensive error handling and memory safety checks.
        """
        try:
            # Pre-execution memory check
            requested_mb = kwargs.get('test_size_mb', 1024)

            # Final memory availability check before execution
            pre_exec_check = self.check_memory_availability(requested_mb)

            if pre_exec_check["is_dangerous"]:
                # Adjust to safe size and log warning
                safe_mb = pre_exec_check["safe_size_mb"]
                kwargs['test_size_mb'] = safe_mb
                self.log(
                    f"Pre-execution safety check: Adjusted {self.test_name} from {requested_mb} MB to {safe_mb} MB "
                    f"(available: {pre_exec_check['available_mb']:.0f} MB)",
                    'warning'
                )

            # Log test execution parameters
            self.log(
                f"Executing {self.test_name} with {kwargs.get('test_size_mb', 'unknown')} MB, "
                f"{kwargs.get('duration_seconds', 'unknown')}s duration",
                'info'
            )

            # Execute the test with error handling
            try:
                result = self.test_function(**kwargs)

                # Validate result format
                if result is None:
                    return {
                        "test_details": {
                            "status": "FAIL",
                            "notes": f"{self.test_name} returned None result"
                        }
                    }

                # Ensure result has proper structure
                if isinstance(result, dict):
                    if 'test_details' not in result and 'status' not in result:
                        # Wrap raw result in proper structure
                        return {
                            "test_details": {
                                "status": "PASS",
                                "notes": f"{self.test_name} completed",
                                "raw_result": result
                            }
                        }
                    return result
                else:
                    # Handle non-dict results
                    return {
                        "test_details": {
                            "status": "PASS" if str(result).lower() in ['pass', 'success', 'true'] else "FAIL",
                            "notes": f"{self.test_name} result: {str(result)}"
                        }
                    }

            except MemoryError as me:
                error_msg = f"Insufficient memory for {self.test_name}: {str(me)}"
                self.log(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": "MemoryError",
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }

            except OSError as oe:
                # Handle OS-level memory allocation errors
                error_msg = f"OS memory allocation error in {self.test_name}: {str(oe)}"
                self.log(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": "OSError",
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }

            except Exception as e:
                # Handle any other test execution errors
                error_msg = f"Error executing {self.test_name}: {str(e)}"
                self.log(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": type(e).__name__,
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }

        except Exception as e:
            # Handle errors in the safety wrapper itself
            error_msg = f"Critical error in RAM test safety wrapper for {self.test_name}: {str(e)}"
            self.log(error_msg, 'error')
            return {
                "test_details": {
                    "status": "FAIL",
                    "notes": error_msg,
                    "error_type": "SafetyWrapperError"
                }
            }

    @staticmethod
    def log_ram_test_summary(profile, callback) -> None:
        """Log a summary of all RAM test configurations used in this test session."""
        try:
            if not profile:
                return

            # Find RAM tests that were configured or executed
            ram_tests_in_profile = [test for test in profile.tests if test in RAM_TEST_PATHS]

            if not ram_tests_in_profile:
                return

            callback("=== RAM Test Configuration Summary ===", 'info')
            callback(f"Profile: {profile.name} ({profile.device_type})", 'info')

            # Get system memory info for context
            try:
                import psutil
                mem_info = psutil.virtual_memory()
                total_gb = mem_info.total / (1024 * 1024 * 1024)
                available_gb = mem_info.available / (1024 * 1024 * 1024)
                callback(f"System Memory: {total_gb:.1f} GB total, {available_gb:.1f} GB available", 'info')
            except:
                callback("System Memory: Unable to determine", 'info')

            # Log configuration for each RAM test
            for test_path in ram_tests_in_profile:
                test_name = test_path.split('.')[-1].replace('run_', '').replace('_test', ' Test').title()
                config = profile.get_ram_test_config(test_path)

                config_source = "profile" if test_path in profile.test_args else "default"
                mode = config.get('test_size_mode', 'percentage')
                value = config.get('test_size_value', 25)
                duration = config.get('duration_seconds', 30)

                if mode == 'percentage':
                    config_desc = f"{value}% of available memory"
                else:
                    config_desc = f"{value} MB absolute"

                # Compute actual test size via calculate_ram_test_size
                try:
                    calc_result = calculate_ram_test_size(config)
                    actual_mb = calc_result.get('test_size_mb', 'unknown')
                except Exception as e:
                    callback(f"Error calculating RAM test size for {test_name}: {e}", 'error')
                    actual_mb = 'unknown'
                callback(
                    f"  {test_name}: {config_desc}, {duration}s duration, actual size: {actual_mb} MB (source: {config_source})",
                    'info'
                )

            callback("=== End RAM Test Summary ===", 'info')

        except Exception as e:
            callback(f"Error logging RAM test summary: {str(e)}", 'error')

    @staticmethod
    def log_ram_test_audit_trail(test_name: str, ram_params: Dict[str, Any], result: Dict[str, Any], callback) -> None:
        """
        Extract RAM test audit trail logging from TestOrchestrator.
        Log comprehensive audit trail for RAM test execution.
        """
        try:
            # Extract configuration details
            config_info = ram_params.get('original_config', {})
            memory_check = ram_params.get('memory_check', {})
            config_source = ram_params.get('config_source', 'unknown')

            # Log configuration audit trail
            audit_info = [
                f"=== RAM Test Audit Trail: {test_name} ===",
                f"Configuration Source: {config_source}",
                f"Original Config Mode: {config_info.get('test_size_mode', 'unknown')}",
                f"Original Config Value: {config_info.get('test_size_value', 'unknown')}",
                f"Configured Duration: {config_info.get('duration_seconds', 'unknown')}s",
                f"Actual Test Size: {ram_params.get('test_size_mb', 'unknown')} MB",
                f"Actual Duration: {ram_params.get('duration_seconds', 'unknown')}s"
            ]

            # Add memory status information
            if memory_check:
                audit_info.extend([
                    f"Available Memory: {memory_check.get('available_mb', 0):.0f} MB",
                    f"Total Memory: {memory_check.get('total_mb', 0):.0f} MB",
                    f"Memory Pressure: {memory_check.get('memory_pressure', 0):.1%}",
                    f"Safety Adjusted: {memory_check.get('needs_adjustment', False)}",
                    f"Adjustment Reason: {memory_check.get('adjustment_reason', 'none')}"
                ])

            # Add calculation metadata if available
            calc_metadata = ram_params.get('calculation_metadata', {})
            if calc_metadata:
                if calc_metadata.get('calculated_from_percentage'):
                    audit_info.append(f"Percentage Calculation: {calc_metadata.get('requested_percentage', 0)}% of {calc_metadata.get('available_memory_mb', 0):.0f} MB")
                if calc_metadata.get('safety_adjusted'):
                    audit_info.append(f"Profile Safety Adjustment: Yes")
                if 'error' in calc_metadata:
                    audit_info.append(f"Calculation Error: {calc_metadata['error']}")

            # Add test result information
            if isinstance(result, dict):
                if 'test_details' in result:
                    test_details = result['test_details']
                    audit_info.extend([
                        f"Test Status: {test_details.get('status', 'unknown')}",
                        f"Test Notes: {test_details.get('notes', 'none')}"
                    ])
                    if 'error_type' in test_details:
                        audit_info.append(f"Error Type: {test_details['error_type']}")

            audit_info.append("=== End Audit Trail ===")

            # Log each line of the audit trail
            for line in audit_info:
                callback(line, 'info')

        except Exception as e:
            callback(f"Error logging RAM test audit trail: {str(e)}", 'error')