import os
import time
import json
import datetime
from typing import List, Dict, Any, Callable, Optional
import logging
import requests
import uuid
try:
    import psutil  # Best-effort for MAC detection
except Exception:  # pragma: no cover
    psutil = None

class ResultManager:

    def __init__(self, log_callback: Optional[Callable[[str, str], None]] = None):
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.log_callback = log_callback if log_callback else self._default_log_callback
        self.test_results_available: bool = False

    def _default_log_callback(self, message: str, level: str = 'info'):
        log_func = getattr(self.logger, level, self.logger.info)
        log_func(message)

    def clear_results(self):
        """Clear previous test results status."""
        self.log_callback('ResultManager: Clearing previous test results status.', 'info')
        self.test_results_available = False

    def save_result_to_file(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any]) -> bool:
        try:
            timestamp = int(time.time())
            results_dir = 'results'
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)
            asset_number = asset_number or 'unknown_asset'
            operator_id = operator_id or 'unknown_operator'
            safe_test_name = test_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
            filename = f'{results_dir}/nexus_result_{asset_number}_{safe_test_name}_{timestamp}.json'
            save_data = {'asset_number': asset_number, 'operator_id': operator_id, 'test_name': test_name, 'timestamp': timestamp, 'result': result}
            with open(filename, 'w') as f:
                json.dump(save_data, f, indent=2)
            self.logger.info(f'Test result saved to {filename}')
            return True
        except Exception as e:
            self.logger.error(f'Error saving test result to file: {str(e)}')
            return False

    def add_result(self, asset_number: str, operator_id: str, test_name: str, result: Dict[str, Any], mac: Optional[str] = None, profile_name: Optional[str] = None):
        status_val = None
        metrics_val: Dict[str, Any] = {}
        started_at_val = None
        finished_at_val = None

        if 'test_details' in result:
            status = result.get('test_details', {}).get('status', 'unknown')
            notes = result.get('test_details', {}).get('notes', '')
            self.logger.info(f'Test result for {test_name}: {status}')
            if notes:
                self.logger.info(f'Notes: {notes}')
            status_val = status
            metrics_val = result.get('test_details', {})
            started_at_val = result.get('started_at')
            finished_at_val = result.get('finished_at') or result.get('finished')
        elif 'status' in result:
            status = result.get('status', 'unknown')
            grade = result.get('grade', 'N/A')
            notes = result.get('notes', '')
            log_level_map = {'pass': 'info', 'fail': 'error', 'warning': 'warning'}
            effective_log_level = log_level_map.get(status.lower(), 'info')
            msg = f'Test result for {test_name}: {status.upper()} (Grade: {grade})'
            if effective_log_level == 'error':
                self.logger.error(msg)
            elif effective_log_level == 'warning':
                self.logger.warning(msg)
            else:
                self.logger.info(msg)
            if notes:
                details_msg = f'Details for {test_name}: ' + ('; '.join(notes) if isinstance(notes, list) else str(notes))
                self.logger.info(details_msg)
            metrics = result.get('metrics', {})
            if metrics:
                self.logger.info(f'Metrics for {test_name}:')
                for key, value in metrics.items():
                    if isinstance(value, (list, dict)):
                        continue
                    self.logger.info(f'  - {key}: {value}')
            status_val = status
            metrics_val = result.get('metrics', {})
            started_at_val = result.get('started_at')
            finished_at_val = result.get('finished_at') or result.get('finished')
        else:
            self.logger.info(f'Test result for {test_name} (unknown format): {str(result)[:200]}')
            metrics_val = result

        if self.save_result_to_file(asset_number, operator_id, test_name, result):
            self.test_results_available = True
        else:
            self.logger.warning(f'Failed to save result for {test_name}. Results availability not changed.')

        # Submit to Nexus using centralized client for better reliability
        try:
            from agent.core.nexus_client import submit_test_result_to_nexus
            
            success = submit_test_result_to_nexus(
                asset_number=asset_number or 'unknown_asset',
                operator_id=operator_id or os.environ.get('NEXUS_OPERATOR') or 'unknown_operator',
                test_name=test_name,
                result_data=result,
                profile_name=profile_name,
                mac=mac
            )
            
            if success:
                self.logger.info(f"Successfully submitted test result to Nexus: {test_name}")
            else:
                self.logger.warning(f"Failed to submit test result to Nexus: {test_name}")
                
        except Exception as e:
            # Best-effort; do not break local flow
            self.logger.debug(f"Nexus submission failed: {e}")

    def _load_and_parse_results(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        parsed_results = []
        for file_path in file_paths:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    data = json.loads(content)
                if not isinstance(data, dict) or not all((k in data for k in ['asset_number', 'operator_id', 'test_name', 'timestamp', 'result'])):
                    self.logger.warning(f'Result file {file_path} has unexpected structure.')
                    continue
                parsed_results.append(data)
                self.logger.debug(f'Successfully parsed result file: {file_path}')
            except FileNotFoundError:
                self.logger.error(f'Result file not found: {file_path}')
            except json.JSONDecodeError as e:
                self.logger.error(f'Error decoding JSON from result file {file_path}: {e}')
            except Exception as e:
                self.logger.error(f'Unexpected error parsing result file {file_path}: {e}')
        return parsed_results

    def consolidate_results_for_asset(self, asset_number: str, operator_id: str) -> Optional[str]:
        results_dir = 'results'
        if not os.path.exists(results_dir):
            self.logger.warning(f"Results directory '{results_dir}' not found. No results to consolidate for asset {asset_number}.")
            return None
        asset_files = []
        for filename in os.listdir(results_dir):
            if filename.startswith(f'nexus_result_{asset_number}_') and filename.endswith('.json'):
                asset_files.append(os.path.join(results_dir, filename))
        if not asset_files:
            self.logger.info(f'No individual result files found for asset {asset_number} in {results_dir}.')
            return None
        self.logger.debug(f'Found {len(asset_files)} result files for asset {asset_number}.')
        all_results_for_asset = self._load_and_parse_results(asset_files)
        if not all_results_for_asset:
            self.logger.warning(f'Could not parse any valid results for asset {asset_number} from found files.')
            return None
        all_results_for_asset.sort(key=lambda x: x.get('timestamp', 0))
        consolidated_data = {'asset_number': asset_number, 'operator_id': operator_id, 'consolidation_timestamp': int(time.time()), 'individual_results_count': len(all_results_for_asset), 'results': all_results_for_asset}
        consolidated_filename = os.path.join(results_dir, f'consolidated_nexus_results_{asset_number}.json')
        try:
            with open(consolidated_filename, 'w') as f:
                json.dump(consolidated_data, f, indent=2)
            self.logger.info(f'Consolidated results for asset {asset_number} saved to {consolidated_filename}')

            # Attempt to push to inventory system
            try:
                from agent.core.inventory_sync import send_consolidated_to_inventory  # Local import to avoid circulars
                send_consolidated_to_inventory(asset_number, consolidated_data)
            except Exception as inv_e:
                self.logger.warning(f'Inventory sync error for asset {asset_number}: {inv_e}')

            return consolidated_filename
        except Exception as e:
            self.logger.error(f'Failed to consolidate results for asset {asset_number}: {e}')
            return None
if __name__ == '__main__':

    def mock_log(message, level='info'):
        print(f'LOG [{level.upper()}]: {message}')

    def mock_get_asset():
        return 'TEST_ASSET_001'

    def mock_get_operator():
        return 'OPERATOR_007'
    if not os.path.exists('results'):
        os.makedirs('results')
    result_manager = ResultManager(log_callback=mock_log)
    print(f'Initial test_results_available: {result_manager.test_results_available}')
    sample_test_result_framework = {'test_details': {'status': 'pass', 'notes': 'CPU test completed successfully.', 'score': 12500, 'cores': 8}, 'started_at': datetime.datetime.now().isoformat(), 'finished_at': (datetime.datetime.now() + datetime.timedelta(seconds=30)).isoformat()}
    result_manager.add_result(mock_get_asset(), mock_get_operator(), 'CPU Performance Test', sample_test_result_framework)
    print(f'After add_result, test_results_available: {result_manager.test_results_available}')
    sample_battery_result = {'status': 'warning', 'grade': 'B', 'notes': ['Cycle count high', 'Reduced capacity'], 'metrics': {'cycle_count': 750, 'current_capacity_mah': 3000, 'design_capacity_mah': 5000}}
    result_manager.add_result(mock_get_asset(), mock_get_operator(), 'Battery Health Check', sample_battery_result)
    asset_files = []
    if os.path.exists('results'):
        for f_name in os.listdir('results'):
            if mock_get_asset() in f_name and f_name.endswith('.json'):
                asset_files.append(os.path.join('results', f_name))
    if asset_files:
        print(f'\nFound result files for {mock_get_asset()}: {asset_files}')
        parsed = result_manager._load_and_parse_results(asset_files)
        print(f'\nParsed {len(parsed)} results:')
        for p_res in parsed:
            print(f"  - Test: {p_res['test_name']}, Status: {p_res['status']}, Notes: {p_res['notes'][:30]}...")
    else:
        print(f'\nNo result files found for {mock_get_asset()} to test parsing.')
    result_manager.clear_results()
    print(f'After clear_results, test_results_available: {result_manager.test_results_available}')