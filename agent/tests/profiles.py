#!/usr/bin/env python3
from __future__ import annotations
"""
Crucible Nexus Test Profiles

This module provides functionality for managing test profiles.
Profiles define sets of tests to run for specific device types.
"""
import json
import os
import logging
from typing import Dict, List, Any, Optional
import psutil
import requests

# Configure logging
logger = logging.getLogger("crucible.tests.profiles")

# Resolve profiles directory with env override and sensible defaults
def _resolve_profiles_dir() -> str:
    # 1) Explicit env var takes precedence
    env_dir = os.getenv("CRUCIBLE_PROFILES_DIR")
    if env_dir and env_dir.strip():
        return env_dir
    # 2) Try project artifacts/profiles if project structure is present
    try:
        module_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(os.path.join(module_dir, os.pardir, os.pardir))  # .../agent/tests -> project root
        artifacts_dir = os.path.join(project_root, "artifacts", "profiles")
        return artifacts_dir
    except Exception:
        pass
    # 3) Fall back to module-adjacent 'profiles'
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), "profiles")

PROFILES_DIR = _resolve_profiles_dir()
LEGACY_PROFILES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "profiles")

# Ensure profiles directory exists
def ensure_profiles_dir():
    """Ensure the profiles directory exists."""
    if not os.path.exists(PROFILES_DIR):
        try:
            os.makedirs(PROFILES_DIR)
            logger.info(f"Created profiles directory: {PROFILES_DIR}")
            return True
        except Exception as e:
            logger.error(f"Failed to create profiles directory: {e}")
            return False


# ---- Nexus integration ----
def _nexus_base_url() -> str:
    # Default to Nexus nginx as per docker-compose mapping
    return os.getenv("NEXUS_BASE_URL", "http://nexus.lan:8080")

def _nexus_base_candidates() -> List[str]:
    """Return a list of Nexus base URL candidates to try in order.

    Priority:
    1) Explicit env var NEXUS_BASE_URL (if set)
    2) localhost:8080
    3) 127.0.0.1:8080
    4) nexus.lan:8080
    """
    env = os.getenv("NEXUS_BASE_URL")
    candidates = []
    if env and env.strip():
        candidates.append(env.strip())
    candidates.extend([
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://nexus.lan:8080",
    ])
    # De-duplicate while preserving order
    seen = set()
    ordered = []
    for c in candidates:
        c = c.rstrip('/')
        if c not in seen:
            seen.add(c)
            ordered.append(c)
    return ordered

def _nexus_profiles_list_url() -> str:
    """Return the Nexus endpoint that serves Crucible-ready payloads.

    This aligns with Nexus router `/api/profiles/crucible`, which returns an array
    of parsed payload objects (name, description, device_type, tests, test_args).
    """
    return os.getenv(
        "NEXUS_PROFILES_LIST_URL",
        f"{_nexus_base_url().rstrip('/')}/api/profiles/crucible",
    )

def fetch_all_profiles_from_nexus() -> List[Profile]:
    """Fetch Crucible-ready profiles from Nexus, trying multiple base URLs."""
    explicit = os.getenv("NEXUS_PROFILES_LIST_URL")
    urls_to_try = []
    if explicit and explicit.strip():
        urls_to_try = [explicit.strip()]
    else:
        urls_to_try = [f"{base}/api/profiles/crucible" for base in _nexus_base_candidates()]

    last_err = None
    for url in urls_to_try:
        try:
            logger.info(f"Fetching profiles list from Nexus: {url}")
            resp = requests.get(url, timeout=5)
            resp.raise_for_status()
            data = resp.json()
            profiles: List[Profile] = []
            if isinstance(data, list):
                for item in data:
                    try:
                        # Items are already Crucible payloads; convert directly
                        p = Profile.from_dict(item)
                        profiles.append(p)
                    except Exception as e:
                        logger.warning(f"Skipping invalid profile from Nexus list: {e}")
                return profiles
            else:
                logger.warning("Unexpected response shape for Nexus profiles list; expected list")
                # Try next URL if any
        except Exception as e:
            last_err = e
            logger.warning(f"Fetch from {url} failed: {e}")

    if last_err:
        logger.warning(f"Failed to fetch profiles list from all Nexus candidates: {last_err}")
    return []

def fetch_profile_from_nexus(profile_name: str) -> Optional[Profile]:
    """Resolve a single profile by name using the Nexus list endpoint.

    Nexus does not expose a by-name GET route; we fetch the list of Crucible
    payloads and match by name (case-insensitive, tolerant to non-alnum).
    """
    try:
        all_from_nexus = fetch_all_profiles_from_nexus()

        def norm(s: str) -> str:
            s = (s or '').strip().lower()
            return ''.join(ch for ch in s if ch.isalnum())

        target_lower = (profile_name or '').strip().lower()
        target_norm = norm(profile_name or '')
        for p in all_from_nexus:
            if (str(p.name).strip().lower() == target_lower) or (norm(p.name) == target_norm):
                return p
        return None
    except Exception as e:
        logger.warning(f"Failed to resolve profile '{profile_name}' from Nexus: {e}")
        return None

# Create the directory when module is loaded and log where we store profiles
if ensure_profiles_dir():
    logger.info(f"Profiles directory in use: {PROFILES_DIR}")


class Profile:
    """Represents a test profile for a specific device type."""

    def __init__(self, name: str, description: str = "", tests: List[str] = None,
                 device_type: str = "Generic", test_args: Dict[str, Dict[str, Any]] = None):
        """
        Initialize a test profile.

        Args:
            name: Profile name
            description: Profile description
            tests: List of test paths to run
            device_type: Type of device (Desktop, Laptop, etc.)
            test_args: Optional arguments for specific tests
        """
        self.name = name
        self.description = description
        self.tests = tests or []
        self.device_type = device_type
        self.test_args = test_args or {}

    def get_ram_test_config(self, test_path: str) -> Dict[str, Any]:
        """
        Get RAM test configuration for a specific test path.
        
        Args:
            test_path: The test path to get configuration for
            
        Returns:
            Dictionary containing RAM test configuration with defaults
        """
        # Default RAM test configuration
        default_config = {
            "test_size_mode": "percentage",  # "percentage" or "absolute"
            "test_size_value": 25,          # 25% or MB value
            "duration_seconds": 30,         # Test duration
            "max_percentage": 50            # Safety limit for percentage mode
        }
        
        # Get test-specific configuration from test_args
        test_config = self.test_args.get(test_path, {})
        
        # Merge with defaults
        config = {**default_config, **test_config}
        
        return config

    def set_ram_test_config(self, test_path: str, config: Dict[str, Any]) -> bool:
        """
        Set RAM test configuration for a specific test path.
        
        Args:
            test_path: The test path to set configuration for
            config: Configuration dictionary
            
        Returns:
            True if configuration is valid and set, False otherwise
        """
        # Validate the configuration
        if not self.validate_ram_test_config(config):
            return False
        
        # Ensure test_args exists for this test path
        if test_path not in self.test_args:
            self.test_args[test_path] = {}
        
        # Update the configuration
        self.test_args[test_path].update(config)
        
        return True

    @staticmethod
    def validate_ram_test_config(config: Dict[str, Any]) -> bool:
        """
        Validate RAM test configuration parameters.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required fields
            mode = config.get("test_size_mode", "percentage")
            if mode not in ["percentage", "absolute"]:
                logger.error(f"Invalid test_size_mode: {mode}. Must be 'percentage' or 'absolute'")
                return False
            
            # Validate test size value
            size_value = config.get("test_size_value")
            if size_value is None or not isinstance(size_value, (int, float)) or size_value <= 0:
                logger.error(f"Invalid test_size_value: {size_value}. Must be a positive number")
                return False
            
            # Validate percentage mode constraints (1-50% range)
            if mode == "percentage":
                if not isinstance(size_value, (int, float)) or size_value < 1 or size_value > 50:
                    logger.error(f"Invalid percentage value: {size_value}%. Must be between 1-50%")
                    return False
                # Ensure it's a reasonable precision (no more than 1 decimal place)
                if isinstance(size_value, float) and round(size_value, 1) != size_value:
                    logger.error(f"Invalid percentage precision: {size_value}%. Maximum 1 decimal place allowed")
                    return False
            
            # Validate absolute mode constraints (positive integers)
            elif mode == "absolute":
                if not isinstance(size_value, (int, float)) or size_value <= 0:
                    logger.error(f"Invalid absolute value: {size_value} MB. Must be a positive number")
                    return False
                # For absolute mode, prefer integer values but allow floats that are whole numbers
                if isinstance(size_value, float) and size_value != int(size_value):
                    logger.error(f"Invalid absolute value: {size_value} MB. Should be a whole number")
                    return False
            
            # Validate duration (5-3600 seconds range)
            duration = config.get("duration_seconds")
            if duration is None or not isinstance(duration, (int, float)) or duration < 5 or duration > 3600:
                logger.error(f"Invalid duration_seconds: {duration}. Must be between 5-3600 seconds")
                return False
            
            # Validate max_percentage if provided
            max_percentage = config.get("max_percentage", 50)
            if not isinstance(max_percentage, (int, float)) or max_percentage < 1 or max_percentage > 50:
                logger.error(f"Invalid max_percentage: {max_percentage}. Must be between 1-50%")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating RAM test configuration: {e}")
            return False

    @staticmethod
    def validate_percentage_mode(percentage: float) -> bool:
        """
        Validate percentage mode configuration (1-50% range).
        
        Args:
            percentage: Percentage value to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(percentage, (int, float)):
                logger.error(f"Percentage must be a number, got {type(percentage)}")
                return False
            
            if percentage < 1 or percentage > 50:
                logger.error(f"Percentage {percentage}% out of range. Must be between 1-50%")
                return False
            
            # Check precision (max 1 decimal place)
            if isinstance(percentage, float) and round(percentage, 1) != percentage:
                logger.error(f"Percentage {percentage}% has too much precision. Maximum 1 decimal place")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating percentage: {e}")
            return False

    @staticmethod
    def validate_absolute_mode(size_mb: float) -> bool:
        """
        Validate absolute mode configuration (positive integers).
        
        Args:
            size_mb: Size in MB to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(size_mb, (int, float)):
                logger.error(f"Size must be a number, got {type(size_mb)}")
                return False
            
            if size_mb <= 0:
                logger.error(f"Size {size_mb} MB must be positive")
                return False
            
            # Should be a whole number (integer or float that equals its integer value)
            if isinstance(size_mb, float) and size_mb != int(size_mb):
                logger.error(f"Size {size_mb} MB should be a whole number")
                return False
            
            # Reasonable upper limit check (1TB = 1,048,576 MB)
            if size_mb > 1048576:
                logger.error(f"Size {size_mb} MB exceeds reasonable limit (1TB)")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating absolute size: {e}")
            return False

    @staticmethod
    def validate_duration(duration_seconds: float) -> bool:
        """
        Validate duration configuration (5-3600 seconds).
        
        Args:
            duration_seconds: Duration in seconds to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(duration_seconds, (int, float)):
                logger.error(f"Duration must be a number, got {type(duration_seconds)}")
                return False
            
            if duration_seconds < 5:
                logger.error(f"Duration {duration_seconds}s too short. Minimum 5 seconds")
                return False
            
            if duration_seconds > 3600:
                logger.error(f"Duration {duration_seconds}s too long. Maximum 3600 seconds (1 hour)")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating duration: {e}")
            return False

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert profile to dictionary for serialization.
        
        Returns:
            Dictionary representation of the profile with all fields
        """
        # Ensure test_args is always included, even if empty
        test_args = self.test_args if self.test_args is not None else {}
        
        return {
            "name": self.name,
            "description": self.description,
            "tests": self.tests,
            "device_type": self.device_type,
            "test_args": test_args
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Profile':
        """
        Create a profile from a dictionary with backward compatibility.
        
        Args:
            data: Dictionary containing profile data
            
        Returns:
            Profile instance with proper defaults for missing fields
        """
        # Handle backward compatibility for profiles without test_args
        test_args = data.get("test_args", {})
        
        # Ensure test_args is a dictionary
        if not isinstance(test_args, dict):
            logger.warning(f"Invalid test_args format in profile data, using empty dict")
            test_args = {}
        
        # Create profile instance
        profile = cls(
            name=data.get("name", "Unnamed Profile"),
            description=data.get("description", ""),
            tests=data.get("tests", []),
            device_type=data.get("device_type", "Generic"),
            test_args=test_args
        )
        
        # Add default RAM test configuration for RAM tests if not present
        profile._ensure_ram_test_defaults()
        
        return profile

    def _ensure_ram_test_defaults(self):
        """
        Ensure RAM test configuration defaults are present for RAM tests in the profile.
        This provides backward compatibility for profiles created before RAM configuration was added.
        """
        # List of RAM test paths that should have default configuration
        ram_test_paths = [
            "agent.tests.visual_ram_test.run_visual_ram_test",
            "agent.tests.web_visual_ram_test.run_web_visual_ram_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.ram_test.run_advanced_ram_test"
        ]
        
        # Default RAM test configuration
        default_ram_config = {
            "test_size_mode": "percentage",
            "test_size_value": 25,
            "duration_seconds": 30,
            "max_percentage": 50
        }
        
        # Add defaults for RAM tests that are in the profile but don't have configuration
        for test_path in self.tests:
            if any(ram_path in test_path for ram_path in ram_test_paths):
                if test_path not in self.test_args:
                    self.test_args[test_path] = {}
                
                # Add missing RAM configuration fields with defaults
                for key, default_value in default_ram_config.items():
                    if key not in self.test_args[test_path]:
                        self.test_args[test_path][key] = default_value
                        
                logger.debug(f"Added default RAM configuration for {test_path}")

    def migrate_legacy_profile(self) -> bool:
        """
        Migrate legacy profile format to current format.
        
        Returns:
            True if migration was performed, False if no migration needed
        """
        migrated = False
        
        # Ensure test_args exists
        if not hasattr(self, 'test_args') or self.test_args is None:
            self.test_args = {}
            migrated = True
            logger.info(f"Migrated profile '{self.name}': Added test_args field")
        
        # Ensure RAM test defaults are present
        original_args_count = len(self.test_args)
        self._ensure_ram_test_defaults()
        
        if len(self.test_args) > original_args_count:
            migrated = True
            logger.info(f"Migrated profile '{self.name}': Added RAM test defaults")
        
        return migrated


# Utility functions for RAM test parameter calculations
def get_system_memory_info() -> Dict[str, float]:
    """
    Get system memory information.
    
    Returns:
        Dictionary containing memory information in MB
    """
    try:
        mem_info = psutil.virtual_memory()
        return {
            "total_mb": mem_info.total / (1024 * 1024),
            "available_mb": mem_info.available / (1024 * 1024),
            "used_mb": mem_info.used / (1024 * 1024),
            "percent_used": mem_info.percent
        }
    except Exception as e:
        logger.error(f"Error getting system memory info: {e}")
        return {
            "total_mb": 0,
            "available_mb": 0,
            "used_mb": 0,
            "percent_used": 0
        }


def calculate_ram_test_size(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate actual RAM test size in MB based on configuration.
    
    Args:
        config: RAM test configuration dictionary
        
    Returns:
        Dictionary containing calculated values and metadata
    """
    try:
        mode = config.get("test_size_mode", "percentage")
        size_value = config.get("test_size_value", 25)
        max_percentage = config.get("max_percentage", 50)
        
        # Get current memory info
        mem_info = get_system_memory_info()
        available_mb = mem_info["available_mb"]
        
        if mode == "percentage":
            # Calculate MB from percentage
            percentage = min(size_value, max_percentage)  # Apply safety limit
            calculated_mb = int(available_mb * percentage / 100)
            
            # Additional safety check - never use more than 25% of available memory
            safe_limit_mb = int(available_mb * 0.25)
            actual_mb = min(calculated_mb, safe_limit_mb)
            
            return {
                "test_size_mb": actual_mb,
                "original_config": config,
                "calculated_from_percentage": True,
                "requested_percentage": size_value,
                "applied_percentage": percentage,
                "safety_adjusted": actual_mb < calculated_mb,
                "available_memory_mb": available_mb,
                "total_memory_mb": mem_info["total_mb"]
            }
        else:
            # Absolute mode - validate against available memory
            requested_mb = int(size_value)
            safe_limit_mb = int(available_mb * 0.8)  # Use at most 80% of available
            actual_mb = min(requested_mb, safe_limit_mb)
            
            return {
                "test_size_mb": actual_mb,
                "original_config": config,
                "calculated_from_percentage": False,
                "requested_mb": requested_mb,
                "safety_adjusted": actual_mb < requested_mb,
                "available_memory_mb": available_mb,
                "total_memory_mb": mem_info["total_mb"]
            }
            
    except Exception as e:
        logger.error(f"Error calculating RAM test size: {e}")
        # Return safe default
        return {
            "test_size_mb": 1024,  # 1GB default
            "original_config": config,
            "calculated_from_percentage": False,
            "error": str(e),
            "safety_adjusted": True,
            "available_memory_mb": 0,
            "total_memory_mb": 0
        }


def validate_cpu_test_config(config: Dict[str, Any]) -> bool:
    """
    Validate CPU test configuration parameters.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        # Validate duration (5-300 seconds range)
        duration = config.get("duration_seconds")
        if duration is not None:
            if not isinstance(duration, (int, float)) or duration < 5 or duration > 300:
                logger.error(f"Invalid duration_seconds: {duration}. Must be between 5-300 seconds")
                return False
        
        # Validate CPU load intensity (50-100% range)
        intensity = config.get("cpu_load_intensity")
        if intensity is not None:
            if not isinstance(intensity, (int, float)) or intensity < 50 or intensity > 100:
                logger.error(f"Invalid cpu_load_intensity: {intensity}. Must be between 50-100%")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating CPU test configuration: {e}")
        return False


def get_percentage_calculations(memory_mb: float) -> Dict[str, int]:
    """
    Get pre-calculated MB values for common percentages.
    
    Args:
        memory_mb: Available memory in MB
        
    Returns:
        Dictionary mapping percentage strings to MB values
    """
    percentages = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
    calculations = {}
    
    for pct in percentages:
        mb_value = int(memory_mb * pct / 100)
        calculations[str(pct)] = mb_value
    
    return calculations


def get_profile_path(profile_name: str) -> str:
    """
    Get the file path for a profile.

    Args:
        profile_name: Name of the profile

    Returns:
        Path to the profile file
    """
    # Sanitize profile name for use as filename
    safe_name = "".join(c if c.isalnum() else "_" for c in profile_name)
    return os.path.join(PROFILES_DIR, f"{safe_name}.json")


def save_profile(profile: Profile) -> bool:
    """
    Save a profile to disk with validation.

    Args:
        profile: Profile to save

    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Saving profile '{profile.name}' with {len(profile.test_args)} test configurations")
        
        # Validate test configurations before saving
        for test_path, test_config in profile.test_args.items():
            logger.debug(f"Validating config for {test_path}: {test_config}")
            
            # Check if this looks like a RAM test configuration
            if any(key in test_config for key in ["test_size_mode", "test_size_value"]):
                logger.debug(f"Validating RAM test configuration for {test_path}")
                if not profile.validate_ram_test_config(test_config):
                    logger.error(f"Invalid RAM test configuration for {test_path} in profile '{profile.name}'")
                    return False
            
            # Check if this looks like a CPU test configuration
            elif any(key in test_config for key in ["cpu_load_intensity"]):
                logger.debug(f"Validating CPU test configuration for {test_path}")
                if not validate_cpu_test_config(test_config):
                    logger.error(f"Invalid CPU test configuration for {test_path} in profile '{profile.name}'")
                    return False
            
            # Check for mixed configurations (both RAM and CPU parameters)
            elif any(key in test_config for key in ["test_size_mode", "test_size_value", "cpu_load_intensity"]):
                logger.debug(f"Validating mixed test configuration for {test_path}")
                # Validate RAM parts if present
                if any(key in test_config for key in ["test_size_mode", "test_size_value"]):
                    if not profile.validate_ram_test_config(test_config):
                        logger.error(f"Invalid RAM test configuration for {test_path} in profile '{profile.name}'")
                        return False
                # Validate CPU parts if present
                if any(key in test_config for key in ["cpu_load_intensity"]):
                    if not validate_cpu_test_config(test_config):
                        logger.error(f"Invalid CPU test configuration for {test_path} in profile '{profile.name}'")
                        return False
        
        profile_path = get_profile_path(profile.name)
        logger.debug(f"Writing profile to {profile_path}")
        with open(profile_path, 'w') as f:
            json.dump(profile.to_dict(), f, indent=2)
        logger.info(f"Successfully saved profile '{profile.name}' to {profile_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save profile '{profile.name}': {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def load_profile(profile_name: str) -> Optional[Profile]:
    """
    Load a profile from disk with automatic migration support.

    Args:
        profile_name: Name of the profile to load

    Returns:
        Loaded profile or None if not found
    """
    try:
        profile_path = get_profile_path(profile_name)
        if not os.path.exists(profile_path):
            # Fallback: tolerant lookup by internal profile 'name' and filename stem
            logger.warning(f"Profile '{profile_name}' not found at {profile_path}. Attempting tolerant lookup.")
            search_dirs = []
            if os.path.exists(PROFILES_DIR):
                search_dirs.append(PROFILES_DIR)
            if os.path.exists(LEGACY_PROFILES_DIR) and LEGACY_PROFILES_DIR != PROFILES_DIR:
                search_dirs.append(LEGACY_PROFILES_DIR)
            if search_dirs:
                def norm(s: str) -> str:
                    s = (s or '').strip().lower()
                    return ''.join(ch for ch in s if ch.isalnum())
                target_lower = (profile_name or '').strip().lower()
                target_norm = norm(profile_name or '')
                for d in search_dirs:
                    for filename in os.listdir(d):
                        if not filename.endswith('.json'):
                            continue
                        try:
                            full_path = os.path.join(d, filename)
                            # Try filename stem match first (ignoring non-alnum)
                            stem = os.path.splitext(filename)[0]
                            if norm(stem) == target_norm:
                                profile_path = full_path
                                logger.info(f"Resolved profile '{profile_name}' by stem match in {d} -> '{filename}'")
                                raise StopIteration
                            # Try internal name match (case-insensitive and ignoring non-alnum)
                            with open(full_path, 'r') as f:
                                data_probe = json.load(f)
                            if isinstance(data_probe, dict):
                                internal_name = str(data_probe.get('name', ''))
                                if internal_name.strip().lower() == target_lower or norm(internal_name) == target_norm:
                                    profile_path = full_path
                                    logger.info(f"Resolved profile '{profile_name}' via internal name match in {d} -> '{filename}'")
                                    raise StopIteration
                        except StopIteration:
                            break
                        except Exception as e_probe:
                            logger.debug(f"Skipping '{filename}' during tolerant lookup in {d}: {e_probe}")
                    if os.path.exists(profile_path):
                        break
            # If still not found locally, try Nexus
            if not os.path.exists(profile_path):
                p = fetch_profile_from_nexus(profile_name)
                if p:
                    # Cache to disk for future runs
                    try:
                        save_profile(p)
                    except Exception:
                        pass
                    return p
                return None

        with open(profile_path, 'r') as f:
            data = json.load(f)

        # Create profile with backward compatibility handling
        profile = Profile.from_dict(data)
        
        # Check if migration was needed and save updated profile
        if profile.migrate_legacy_profile():
            logger.info(f"Profile '{profile.name}' was migrated to current format")
            # Save the migrated profile back to disk
            if save_profile(profile):
                logger.info(f"Saved migrated profile '{profile.name}'")
            else:
                logger.warning(f"Failed to save migrated profile '{profile.name}'")
        
        logger.info(f"Loaded profile '{profile.name}' from {profile_path}")
        return profile
    except Exception as e:
        logger.error(f"Failed to load profile '{profile_name}': {e}")
        return None


def get_all_profiles() -> List[Profile]:
    """
    Get all available profiles with automatic migration support.

    Returns:
        List of all profiles
    """
    profiles = []

    search_dirs = []
    if os.path.exists(PROFILES_DIR):
        search_dirs.append(PROFILES_DIR)
    else:
        logger.warning(f"Profiles directory not found: {PROFILES_DIR}")
    if os.path.exists(LEGACY_PROFILES_DIR) and LEGACY_PROFILES_DIR != PROFILES_DIR:
        search_dirs.append(LEGACY_PROFILES_DIR)

    seen_names = set()
    for d in search_dirs:
        for filename in os.listdir(d):
            if filename.endswith(".json"):
                try:
                    with open(os.path.join(d, filename), 'r') as f:
                        data = json.load(f)
                    # Create profile with backward compatibility handling
                    profile = Profile.from_dict(data)
                    # Deduplicate by profile name
                    if profile.name in seen_names:
                        continue
                    seen_names.add(profile.name)
                    # Check if migration was needed and save updated profile
                    if profile.migrate_legacy_profile():
                        logger.info(f"Profile '{profile.name}' was migrated to current format")
                        # Save the migrated profile back to disk
                        if save_profile(profile):
                            logger.debug(f"Saved migrated profile '{profile.name}'")
                        else:
                            logger.warning(f"Failed to save migrated profile '{profile.name}'")
                    profiles.append(profile)
                except Exception as e:
                    logger.error(f"Failed to load profile from {os.path.join(d, filename)}: {e}")

    # If local is empty, try Nexus as a source of truth
    if not profiles:
        nexus_profiles = fetch_all_profiles_from_nexus()
        # Optionally cache them locally for offline operations
        for p in nexus_profiles:
            try:
                save_profile(p)
            except Exception:
                pass
        return nexus_profiles

    return profiles


def delete_profile(profile_name: str) -> bool:
    """
    Delete a profile.

    Args:
        profile_name: Name of the profile to delete

    Returns:
        True if successful, False otherwise
    """
    try:
        profile_path = get_profile_path(profile_name)
        if not os.path.exists(profile_path):
            logger.warning(f"Profile '{profile_name}' not found at {profile_path}")
            return False

        os.remove(profile_path)
        logger.info(f"Deleted profile '{profile_name}' from {profile_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to delete profile '{profile_name}': {e}")
        return False


# Default profiles
DEFAULT_PROFILES = [
    Profile(
        name="Desktop",
        description="Standard tests for desktop computers",
        device_type="Desktop",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.ram_test.run_advanced_ram_test",
            "agent.tests.display_test.run_lcd_test_gui",
            "agent.tests.keyboard_test.run_keyboard_test",
        ],
        test_args={
            # Desktop RAM tests - more aggressive settings for desktop hardware
            "agent.tests.ram_test.run_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 30,
                "duration_seconds": 45
            },
            "agent.tests.ram_test.run_advanced_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 35,
                "duration_seconds": 60
            },
            "agent.tests.visual_ram_test.run_visual_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 30,
                "duration_seconds": 45
            },
            "agent.tests.web_visual_ram_test.run_web_visual_ram_test": {
                "test_size_mode": "percentage", 
                "test_size_value": 30,
                "duration_seconds": 45
            }
        }
    ),
    Profile(
        name="Laptop",
        description="Standard tests for laptop computers",
        device_type="Laptop",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
            "agent.tests.display_test.run_lcd_test_gui",
            "agent.tests.keyboard_test.run_keyboard_test",
            "agent.tests.pointing_device_test.run_pointing_device_test",
            "agent.tests.battery_test.run_battery_test",  # Add battery health test to laptop profile
        ],
        test_args={
            # Laptop RAM tests - more conservative settings for laptop hardware
            "agent.tests.ram_test.run_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 20,
                "duration_seconds": 30
            },
            "agent.tests.visual_ram_test.run_visual_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 20,
                "duration_seconds": 30
            },
            "agent.tests.web_visual_ram_test.run_web_visual_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 20,
                "duration_seconds": 30
            }
        }
    ),
    Profile(
        name="Battery",
        description="Battery health and performance tests for laptops and portable devices",
        device_type="Laptop",
        tests=[
            "agent.tests.battery_test.run_battery_test",
            "agent.tests.battery_test.run_battery_discharge_test",
            "agent.tests.battery_test.run_battery_charge_test",
            "agent.tests.battery_test.run_battery_full_assessment"
        ],
        test_args={
            "agent.tests.battery_test.run_battery_discharge_test": {
                "duration_seconds": 120  # 2 minute discharge test
            },
            "agent.tests.battery_test.run_battery_charge_test": {
                "duration_seconds": 180  # 3 minute charge test
            }
        }
    ),
    Profile(
        name="Visual Tests",
        description="Visual tests for interactive diagnostics",
        device_type="Any",
        tests=[
            "agent.tests.visual_cpu_test.run_visual_cpu_test",
            "agent.tests.visual_ram_test.run_visual_ram_test",
        ],
        test_args={
            # Visual Tests - moderate settings for demonstration purposes
            "agent.tests.visual_ram_test.run_visual_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 25,
                "duration_seconds": 60
            }
        }
    ),
    Profile(
        name="Quick Check",
        description="Quick basic tests for rapid diagnostics",
        device_type="Any",
        tests=[
            "agent.tests.cpu_test.run_basic_cpu_test",
            "agent.tests.ram_test.run_ram_test",
        ],
        test_args={
            # Quick Check - minimal RAM test settings for speed
            "agent.tests.ram_test.run_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 15,
                "duration_seconds": 20
            }
        }
    ),
    Profile(
        name="Stress Test",
        description="Extended stress tests for stability verification",
        device_type="Any",
        tests=[
            "agent.tests.cpu_test.run_cpu_stress_test",
            "agent.tests.ram_test.run_advanced_ram_test",
        ],
        test_args={
            "agent.tests.cpu_test.run_cpu_stress_test": {
                "duration_seconds": 60  # Longer stress test
            },
            # Stress Test - aggressive RAM test settings for thorough testing
            "agent.tests.ram_test.run_advanced_ram_test": {
                "test_size_mode": "percentage",
                "test_size_value": 40,
                "duration_seconds": 120
            }
        }
    ),
    Profile(
        name="Secure Wipe",
        description="Secure data wiping for device decommissioning",
        device_type="Any",
        tests=[
            "agent.tests.drive_wipe_test.run_secure_wipe_test",
            "agent.tests.drive_wipe_test.run_wipe_verification_test",
        ]
    )
]


def create_default_profiles():
    """Create default profiles if they don't exist."""
    # Ensure profiles directory exists
    if not ensure_profiles_dir():
        logger.error("Failed to create profiles directory, cannot create default profiles")
        return

    # Create each default profile if it doesn't exist
    for profile in DEFAULT_PROFILES:
        profile_path = get_profile_path(profile.name)
        if not os.path.exists(profile_path):
            if save_profile(profile):
                logger.info(f"Created default profile: {profile.name}")
            else:
                logger.error(f"Failed to create default profile: {profile.name}")


# Default profiles are no longer created automatically at startup.
# To create default profiles, call create_default_profiles() explicitly from the profile editor or setup script.

