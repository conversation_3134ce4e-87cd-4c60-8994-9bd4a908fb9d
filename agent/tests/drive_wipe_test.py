"""Secure Drive Wipe Test Module

This module performs **real** secure drive wiping operations for the Crucible Nexus
ITAD toolkit.  It runs *destructive* commands such as `dd`, `shred`, `blkdiscard`,
`hdparm`, and `nvme` against raw block devices and therefore **MUST** be executed
from a Linux live environment with root privileges.  Use at your own risk – data
is permanently destroyed.
"""
from __future__ import annotations

import platform
import datetime
import os
import platform
import subprocess
import time
import shutil
from typing import Dict, Any, List, Optional, Tuple, Union

import psutil
from agent.core import nexus_credits
from agent.hardware import system_info

import threading
import hashlib
from agent.tests import test_framework # Import the module
from agent.tests.test_framework import TestCategory, TestSeverity, TestStatus, test # Keep these direct for convenience

# New GUI window
from agent.gui.drive_wipe_gui import DriveWipeWindow

# ---------------------------------------------------------------------------
# Helper / backend functions – ACTUAL destructive operations
# ---------------------------------------------------------------------------

ZERO_MB_SHA256 = (
    "122dfdb93ab951c1efc849390009083098f5800a1bf27359d877861791902553"  # 1 MiB zeros
)


def calculate_boundary_hashes(device_path: str, stage: str, mb_to_read: int = 1):
    """Return SHA-256 of first and last *mb_to_read* of *device_path*.

    On platforms where we cannot access block devices (or when not root), this
    function returns empty hashes. No exception is raised.
    """
    try:
        # Determine size in 512-byte blocks via blockdev
        sz_out = subprocess.check_output(["blockdev", "--getsz", device_path], text=True).strip()
        total_blocks = int(sz_out)
        block_bytes = total_blocks * 512
        offset = block_bytes - mb_to_read * 1024 * 1024

        # Read first chunk
        first = subprocess.check_output(
            [
                "dd",
                f"if={device_path}",
                "bs=1M",
                f"count={mb_to_read}",
            ],
            stderr=subprocess.DEVNULL,
        )
        first_hash = hashlib.sha256(first).hexdigest()

        # Read last chunk
        last = subprocess.check_output(
            [
                "dd",
                f"if={device_path}",
                "bs=1M",
                f"skip={offset // (1024*1024)}",
                f"count={mb_to_read}",
            ],
            stderr=subprocess.DEVNULL,
        )
        last_hash = hashlib.sha256(last).hexdigest()
    except Exception:
        first_hash = last_hash = ""

    return {
        f"{stage}_first_1mb_sha256": first_hash,
        f"{stage}_last_1mb_sha256": last_hash,
    }


def _require_root():
    """Raise RuntimeError if the process is not running as root."""
    if hasattr(os, "geteuid") and os.geteuid() != 0:
        raise RuntimeError("Drive wiping requires root privileges – please run as root.")


def _drive_size_bytes(device_path: str) -> int | None:
    """Return size in bytes for *device_path* using blockdev."""
    try:
        out = subprocess.check_output(["blockdev", "--getsize64", device_path], text=True).strip()
        return int(out)
    except Exception:
        return None


def _dd_fill(input_device: str, device_path: str, label: str, progress_cb, log_cb):
    """Helper that uses *dd* to write *input_device* (zero or urandom) over *device_path*."""
    _require_root()
    size_bytes = _drive_size_bytes(device_path)
    log_cb(f"{label} wipe started on {device_path}")
    # The progress_cb now expects a stage.
    progress_cb(device_path, 0, 0, "initialising", "wiping")

    cmd = [
        "dd",
        f"if={input_device}",
        f"of={device_path}",
        "bs=16M",
        "oflag=direct,dsync",
        "status=progress",
    ]
    proc = subprocess.Popen(cmd, stderr=subprocess.PIPE, text=True)

    if size_bytes:
        for line in proc.stderr:
            parts = line.strip().split()
            if parts and parts[0].isdigit():
                written = int(parts[0])
                pct = min(100.0, written / size_bytes * 100)
                if progress_cb(device_path, 0, pct, f"{pct:.1f}%", "wiping"):
                    proc.terminate() # Stop if callback signals cancellation
                    log_cb(f"{label} wipe on {device_path} cancelled by callback.", "warning")
                    return {"status": "cancelled", "details": f"{label} dd wipe cancelled", "errors": "Cancelled by user"}
    proc.wait()

    status = "pass" if proc.returncode == 0 else "fail"
    if proc.returncode != 0 and proc.returncode != -15: # -15 is SIGTERM
        log_cb(f"{label} wipe finished on {device_path} with errors (exit {proc.returncode})", "error")
        return {"status": "fail", "details": f"{label} dd wipe", "errors": f"dd returned {proc.returncode}"}

    log_cb(f"{label} wipe finished on {device_path} (exit {proc.returncode})")
    return {"status": status, "details": f"{label} dd wipe", "errors": "" if proc.returncode == 0 else f"dd returned {proc.returncode}"}


def _get_smart_status(device_path: str, log_cb) -> str:
    """Checks the S.M.A.R.T. health status of a drive using smartctl.

    Args:
        device_path: The path to the drive (e.g., /dev/sda).
        log_cb: Callback for logging messages.

    Returns:
        A string indicating the S.M.A.R.T. status (e.g., "PASSED", "FAILED",
        "smartctl_not_found", "UNKNOWN_FORMAT", "EXECUTION_ERROR").
    """
    if shutil.which("smartctl") is None:
        log_cb(f"smartctl not found. S.M.A.R.T. check skipped for {device_path}.", "warning")
        return "smartctl_not_found"

    try:
        # The command `smartctl -H` or `smartctl --health` shows overall health assessment.
        cmd = ["smartctl", "-H", device_path]
        proc = subprocess.run(cmd, capture_output=True, text=True, check=False) # check=False to parse output manually

        if proc.returncode != 0:
            # smartctl exit codes are bitmasks. 0 means no errors.
            # See `man smartctl` EXIT STATUS section for details.
            # For simplicity here, any non-zero exit code is treated as an issue worth noting,
            # though it might not always mean the drive is "FAILED".
            log_cb(f"smartctl command for {device_path} returned exit code {proc.returncode}. Stderr: {proc.stderr.strip()}", "warning")
            # Further parsing of specific bits could be done here if needed.
            # For now, if health assessment isn't clearly PASSED, we'll be cautious.

        output = proc.stdout.strip()

        # Search for common health indicators.
        # Example output for a healthy drive:
        # smartctl 7.2 2020-12-30 r5155 [x86_64-linux-5.10.0-19-amd64] (local build)
        # Copyright (C) 2002-20, Bruce Allen, Christian Franke, www.smartmontools.org
        #
        # === START OF READ SMART DATA SECTION ===
        # SMART overall-health self-assessment test result: PASSED
        #
        # Example for a failing drive:
        # SMART overall-health self-assessment test result: FAILED
        #
        # Some drives might report "OK" or similar positive status.
        # Adaptec RAID controllers might show "SMART Health Status: OK"

        if "SMART overall-health self-assessment test result: PASSED" in output:
            return "PASSED"
        elif "SMART Health Status: OK" in output: # For some controllers
             return "PASSED" # Treat as PASSED
        elif "SMART overall-health self-assessment test result: FAILED" in output:
            log_cb(f"S.M.A.R.T. status for {device_path}: FAILED. Output: {output}", "error")
            return "FAILED"
        elif "FAILING_NOW" in output: # Another indicator of failure
            log_cb(f"S.M.A.R.T. status for {device_path}: FAILING_NOW. Output: {output}", "error")
            return "FAILED"
        else:
            # This case handles outputs where the specific "PASSED" or "FAILED" strings are not found.
            # It could be due to unusual smartctl versions, drive types, or if smartctl errored but still exited 0.
            log_cb(f"Could not determine S.M.A.R.T. status from output for {device_path}. Output: {output}", "warning")
            return "UNKNOWN_FORMAT"

    except subprocess.CalledProcessError as e:
        log_cb(f"Error executing smartctl for {device_path}: {e}. Stderr: {e.stderr.decode()}", "error")
        return "EXECUTION_ERROR"
    except Exception as e:
        log_cb(f"An unexpected error occurred during S.M.A.R.T. check for {device_path}: {e}", "error")
        return "UNEXPECTED_ERROR"


def _create_ones_file(size_mb: int = 1) -> str:
    """Creates a temporary file filled with ones.

    Args:
        size_mb: The size of the file to create in megabytes.

    Returns:
        The path to the temporary file.
    """
    import tempfile
    fd, temp_file_path = tempfile.mkstemp(suffix=".ones", prefix="crucible-")
    os.close(fd)  # Close the file descriptor, dd will open it

    # Fill the file with ones using dd
    # We use /dev/zero and tr to replace null bytes with ones.
    # This is generally faster than writing ones directly in Python for large files.
    block_size = 1024 * 1024  # 1MB
    count = size_mb
    # Use a pipe to feed 'tr' with null bytes from /dev/zero, then 'tr' replaces them with \377 (ones in octal)
    # and dd writes this to the temp file.
    dd_command = f"dd if=/dev/zero bs={block_size} count={count} | tr '\\000' '\\377' | dd of={temp_file_path} bs={block_size} count={count} oflag=dsync"
    try:
        subprocess.run(dd_command, shell=True, check=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
    except subprocess.CalledProcessError as e:
        # Cleanup and re-raise if dd or tr fails
        os.remove(temp_file_path)
        raise RuntimeError(f"Failed to create ones file: {e.stderr.decode()}") from e
    return temp_file_path


def _run_zero_fill(device_path, _method_name, progress_cb, log_cb):
    return _dd_fill("/dev/zero", device_path, "Zero-fill", progress_cb, log_cb)


def _run_random_fill(device_path, _method_name, progress_cb, log_cb):
    """Fills the drive with random data and returns a hash of the first 1MB."""
    fill_result = _dd_fill("/dev/urandom", device_path, "Random-fill", progress_cb, log_cb)

    sample_hash = None
    if fill_result["status"] == "pass":
        log_cb(f"Random fill for {device_path} complete, calculating sample hash of first 1MB.", "info")
        try:
            # Read first 1MB chunk
            first_mb_data = subprocess.check_output(
                [
                    "dd",
                    f"if={device_path}",
                    "bs=1M",
                    "count=1",
                ],
                stderr=subprocess.DEVNULL,
            )
            sample_hash = hashlib.sha256(first_mb_data).hexdigest()
            log_cb(f"Sample hash (first 1MB) for {device_path} after random fill: {sample_hash}", "info")
            fill_result["post_fill_sample_sha256"] = sample_hash
        except Exception as e:
            log_cb(f"Could not calculate sample hash for {device_path} after random fill: {e}", "error")
            fill_result["errors"] = (fill_result.get("errors", "") + "; Failed to calculate sample hash").strip("; ")
            # Not changing status to fail here, as the fill itself might have succeeded.
            # The verification step will handle the missing hash.

    return fill_result


def _run_three_pass_fill(device_path, _method_name, progress_cb, log_cb):
    """Performs a three-pass wipe: zeros, ones, then zeros again."""
    _require_root()
    log_cb(f"Three-pass wipe started on {device_path}")
    results = []
    errors = []
    overall_status = "pass"

    # Pass 1: Zero fill
    progress_cb(device_path, 0, 0, "pass 1/3 (zeros)", "wiping")
    res1 = _dd_fill("/dev/zero", device_path, "Pass 1/3 (Zeros)", lambda d, o, p, m, s: progress_cb(d, o, p / 3, f"pass 1/3 (zeros) {p:.1f}%", s), log_cb)
    results.append(res1)
    if res1["status"] != "pass":
        errors.append(f"Pass 1 (zeros) failed: {res1.get('errors', 'Unknown error')}")
        overall_status = "fail"

    # Pass 2: Ones fill
    ones_file_path = None
    if overall_status == "pass":
        progress_cb(device_path, 0, 33.3, "pass 2/3 (ones) - creating pattern file", "wiping")
        try:
            ones_file_size_mb = 16
            ones_file_path = _create_ones_file(size_mb=ones_file_size_mb)
            log_cb(f"Temporary ones-pattern file created: {ones_file_path} ({ones_file_size_mb}MB)")
            progress_cb(device_path, 0, 33.3, "pass 2/3 (ones)", "wiping")
            res2 = _dd_fill(ones_file_path, device_path, "Pass 2/3 (Ones)", lambda d, o, p, m, s: progress_cb(d, o, 33.3 + (p / 3), f"pass 2/3 (ones) {p:.1f}%", s), log_cb)
            results.append(res2)
            if res2["status"] != "pass":
                errors.append(f"Pass 2 (ones) failed: {res2.get('errors', 'Unknown error')}")
                overall_status = "fail"
        except RuntimeError as e:
            errors.append(f"Pass 2 (ones) failed during pattern file creation: {str(e)}")
            overall_status = "fail"
            results.append({"status": "fail", "details": "Pass 2/3 (Ones) - pattern creation failed", "errors": str(e)})
        finally:
            if ones_file_path and os.path.exists(ones_file_path):
                os.remove(ones_file_path)
                log_cb(f"Temporary ones-pattern file removed: {ones_file_path}")

    # Pass 3: Zero fill
    if overall_status == "pass":
        progress_cb(device_path, 0, 66.6, "pass 3/3 (zeros)", "wiping")
        res3 = _dd_fill("/dev/zero", device_path, "Pass 3/3 (Zeros)", lambda d, o, p, m, s: progress_cb(d, o, 66.6 + (p / 3), f"pass 3/3 (zeros) {p:.1f}%", s), log_cb)
        results.append(res3)
        if res3["status"] != "pass":
            errors.append(f"Pass 3 (zeros) failed: {res3.get('errors', 'Unknown error')}")
            overall_status = "fail"

    progress_cb(device_path, 0, 100, "complete", "wiping")
    log_cb(f"Three-pass wipe finished on {device_path} (Overall: {overall_status})")

    final_details = "Three-pass wipe (zeros, ones, zeros)"
    if errors:
        final_details += f" - Completed with errors: {'; '.join(errors)}"

    return {
        "status": overall_status,
        "details": final_details,
        "errors": "; ".join(errors) if errors else ""
    }


def _run_shred(device_path: str, passes: int, label: str, progress_cb, log_cb):
    _require_root()
    log_cb(f"{label} started on {device_path}")
    progress_cb(device_path, 0, 0, "initialising", "wiping")

    cmd = ["shred", "-v", "-n", str(passes), "-z", device_path]
    proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    total_passes = passes + 1
    current_pass = 0
    for line in proc.stderr: # Shred reports progress on stderr
        if "pass" in line.lower():
            try:
                part = line.split("pass", 1)[1].split()[0]
                done, total = part.split("/")
                current_pass = int(done)
                pct = current_pass / total_passes * 100
                if progress_cb(device_path, 0, pct, f"pass {current_pass}/{total_passes}", "wiping"):
                    proc.terminate()
                    return {"status": "cancelled", "details": label, "errors": "Cancelled by user"}
            except Exception:
                pass # Ignore parsing errors
    proc.wait()
    status = "pass" if proc.returncode == 0 else "fail"
    log_cb(f"{label} finished on {device_path} (exit {proc.returncode})")
    return {"status": status, "details": label, "errors": "" if proc.returncode == 0 else f"shred returned {proc.returncode}"}


def _run_dodshort(device_path, _method_name, progress_cb, log_cb):
    return _run_shred(device_path, 3, "DoD 5220.22-M (3-pass)", progress_cb, log_cb)


def _run_gutmann(device_path, _method_name, progress_cb, log_cb):
    return _run_shred(device_path, 35, "Gutmann (35-pass)", progress_cb, log_cb)


def _run_ata_secure_erase(device_path, _method_name, progress_cb, log_cb):
    _require_root()
    if shutil.which("hdparm") is None:
        return {"status": "fail", "details": "hdparm not found", "errors": "hdparm missing"}
    log_cb(f"ATA Secure Erase started on {device_path}")
    progress_cb(device_path, 0, 0, "locking", "wiping")
    drive_size = _drive_size_bytes(device_path)
    if drive_size is not None and drive_size < 1048576:
        log_cb(f"Drive {device_path} appears empty with size {drive_size} bytes. Treating as pass for ATA secure erase.", "info")
        progress_cb(device_path, 0, 100, "complete", "wiping")
        return {"status": "pass", "details": "ATA secure erase skipped: empty drive", "errors": ""}
    result = subprocess.run(["hdparm", "--user-master", "u", "--security-set-pass", "NULL", device_path], capture_output=True, text=True)
    if result.returncode != 0:
        log_cb(f"ATA Secure Erase failed during security-set-pass on {device_path}: Return code: {result.returncode}. Stdout: {result.stdout.strip()}. Stderr: {result.stderr.strip()}", "error")
        return {"status": "fail", "details": "ATA secure erase failed at security-set-pass", "errors": f"Return code: {result.returncode}. Drive size: {drive_size} bytes. Stderr: {result.stderr.strip()}"}
    progress_cb(device_path, 0, 25, "erasing", "wiping")
    result = subprocess.run(["hdparm", "--user-master", "u", "--security-erase", "NULL", device_path], capture_output=True, text=True)
    if result.returncode != 0:
        log_cb(f"ATA Secure Erase failed during security-erase on {device_path}: Return code: {result.returncode}. Stdout: {result.stdout.strip()}. Stderr: {result.stderr.strip()}", "error")
        return {"status": "fail", "details": "ATA secure erase failed during erase command", "errors": f"Return code: {result.returncode}. Drive size: {drive_size} bytes. Stderr: {result.stderr.strip()}"}
    progress_cb(device_path, 0, 100, "complete", "wiping")
    return {"status": "pass", "details": "ATA Secure Erase complete", "errors": ""}


def _run_nvme_sanitize(device_path, _method_name, progress_cb, log_cb):
    _require_root()
    if shutil.which("nvme") is None:
        return {"status": "fail", "details": "nvme-cli not found", "errors": "nvme missing"}
    log_cb(f"NVMe Sanitize started on {device_path}")
    progress_cb(device_path, 0, 0, "sanitizing", "wiping")
    cmd = ["nvme", "sanitize", device_path, "-a", "2", "-T", "1"]
    proc = subprocess.run(cmd, capture_output=True, text=True)
    if proc.returncode != 0:
        return {"status": "fail", "details": "NVMe sanitize failed", "errors": proc.stderr.strip()}
    progress_cb(device_path, 0, 100, "complete", "wiping")
    return {"status": "pass", "details": "NVMe sanitize complete", "errors": ""}


def _run_blkdiscard(device_path, _method_name, progress_cb, log_cb):
    _require_root()
    if shutil.which("blkdiscard") is None:
        return {"status": "fail", "details": "blkdiscard not found", "errors": "blkdiscard missing"}
    log_cb(f"blkdiscard started on {device_path}")
    progress_cb(device_path, 0, 0, "discarding", "wiping")
    proc = subprocess.run(["blkdiscard", "-f", device_path], capture_output=True, text=True)
    status = "pass" if proc.returncode == 0 else "fail"
    errors = ""
    if status == "fail":
        errors = f"blkdiscard failed with exit code {proc.returncode}."
        if proc.stdout:
            errors += f" Stdout: {proc.stdout.strip()}"
        if proc.stderr:
            errors += f" Stderr: {proc.stderr.strip()}"
    progress_cb(device_path, 0, 100, "complete", "wiping")
    return {"status": status, "details": "blkdiscard", "errors": errors.strip()}


WIPE_METHOD_MAP = {
    "zero_fill": _run_zero_fill,
    "random_fill": _run_random_fill,
    "dodshort": _run_dodshort,
    "gutmann": _run_gutmann,
    "ata_secure_erase": _run_ata_secure_erase,
    "nvme_sanitize": _run_nvme_sanitize,
    "blkdiscard": _run_blkdiscard,
    "three_pass_fill": _run_three_pass_fill,
}


def perform_single_wipe_threaded(
    drive_path: str,
    method_name: str,
    drive_info: Dict[str, Any],
    progress_cb_gui,
    log_cb_gui,
    result_cb_main_gui,
):
    """Real single-drive wipe executed in its own thread."""
    start_ts = datetime.datetime.utcnow().isoformat()

    # Attempt to start a Nexus credit operation for this drive wipe (non-fatal)
    nexus_op_id = None
    try:
        device_serial = (drive_info.get("serial") or drive_info.get("serial_number") or drive_info.get("wwn") or system_info.get_machine_serial_number() or "UNKNOWN")
        device_model = (drive_info.get("model") or drive_info.get("name") or system_info.get_model() or system_info.get_system_product_name() or "UNKNOWN")
        nexus_op_id = nexus_credits.start_operation(
            operation_type=nexus_credits.OperationType.STANDARD_WIPE,
            device_serial=str(device_serial),
            device_model=str(device_model),
            metadata={"drive_path": drive_path, "method": method_name},
        )
        if nexus_op_id:
            log_cb_gui(f"Nexus credits: started wipe operation id={nexus_op_id} for {drive_path}", "info")
    except Exception as e:
        log_cb_gui(f"Nexus credits: failed to start wipe operation for {drive_path}: {e}", "warning")

    # Perform S.M.A.R.T. check before wipe
    log_cb_gui(f"Performing S.M.A.R.T. check for {drive_path}...", "info")
    progress_cb_gui(drive_path, 0, 0, "S.M.A.R.T. check", "verifying")
    smart_status = _get_smart_status(drive_path, log_cb_gui)
    log_cb_gui(f"S.M.A.R.T. status for {drive_path}: {smart_status}", "info")

    log_cb_gui(f"Calculating pre-wipe hash for {drive_path}...", "info")
    progress_cb_gui(drive_path, 0, 0, "Pre-wipe hashing", "hashing")
    pre_hash = calculate_boundary_hashes(drive_path, "pre")

    log_cb_gui(f"Starting wipe for {drive_path} using method: {method_name}", "info")

    runner = WIPE_METHOD_MAP.get(method_name, _run_zero_fill)
    res = runner(drive_path, method_name, progress_cb_gui, log_cb_gui)

    if res.get("status") == "cancelled":
        final_result = {
            "device_path": drive_path, "method": method_name, "status": "cancelled",
            "details": "Operation cancelled by user.", "errors": res.get("errors", ""),
            "verification": "not_applicable", "hashes": pre_hash,
            "smart_status_pre_wipe": smart_status, "start_time": start_ts,
            "end_time": datetime.datetime.utcnow().isoformat()
        }
        result_cb_main_gui(drive_path, final_result)
        return

    log_cb_gui(f"Calculating post-wipe hash for {drive_path}...", "info")
    progress_cb_gui(drive_path, 0, 95, "Post-wipe hashing", "verifying")
    post_wipe_sample_sha256 = res.pop("post_fill_sample_sha256", None)
    post_hash = calculate_boundary_hashes(drive_path, "post")
    if post_wipe_sample_sha256:
        post_hash["post_wipe_sample_sha256"] = post_wipe_sample_sha256

    log_cb_gui(f"Verifying wipe for {drive_path}...", "info")
    progress_cb_gui(drive_path, 0, 98, "Verifying", "verifying")
    verification = "not_applicable"
    if method_name == "zero_fill" or method_name == "three_pass_fill":
        if res["status"] == "pass" and post_hash.get("post_first_1mb_sha256") == ZERO_MB_SHA256 and post_hash.get("post_last_1mb_sha256") == ZERO_MB_SHA256:
            verification = "pass: first and last 1MB are zeroed"
        elif res["status"] == "pass":
            verification = "fail: first and/or last 1MB are not zeroed"
        else:
            verification = "fail: wipe command failed"
            if post_hash.get("post_first_1mb_sha256") != ZERO_MB_SHA256 or post_hash.get("post_last_1mb_sha256") != ZERO_MB_SHA256:
                verification += " and first and/or last 1MB are not zeroed"
    elif method_name == "random_fill":
        if res["status"] == "pass" and post_wipe_sample_sha256:
            if post_wipe_sample_sha256 == post_hash.get("post_first_1mb_sha256"):
                verification = "pass: command completed, first 1MB hash matches sample taken post-fill"
            else:
                verification = "warn: command completed, but first 1MB hash changed between post-fill sample and final check."
            verification += " (random data not directly verifiable by fixed pattern)"
        elif res["status"] == "pass":
            verification = "warn: command completed, but sample hash of random data was not obtained. Verification partial."
        else:
            verification = "fail: wipe command failed"
    elif method_name == "blkdiscard":
        if res["status"] == "pass":
            verification = "pass: blkdiscard successful"
        else:
            verification = "fail: blkdiscard command failed"
    elif method_name in WIPE_METHOD_MAP:
        if res["status"] == "pass":
            verification = "pass: command completed successfully"
        else:
            verification = "fail: command failed"

    log_cb_gui(f"Verification result for {drive_path} ({method_name}): {verification}", "info")
    if res.get("errors"):
        log_cb_gui(f"Errors encountered for {drive_path} ({method_name}): {res.get('errors')}", "error")

    progress_cb_gui(drive_path, 0, 100, "Complete", "complete")

    final = {
        "device_path": drive_path,
        "method": method_name,
        "smart_status_pre_wipe": smart_status,
        "status": res["status"],
        "details": res.get("details"),
        "errors": res.get("errors"),
        "verification": verification,
        "hashes": {**pre_hash, **post_hash},
        "start_time": start_ts,
        "end_time": datetime.datetime.utcnow().isoformat(),
    }

    if nexus_op_id:
        try:
            success = (res.get("status") == "pass")
            meta = {"verification": verification}
            ok = nexus_credits.complete_operation(
                operation_id=nexus_op_id,
                success=success,
                metadata=meta,
                error_message=res.get("errors") if not success else None,
            )
            if ok:
                log_cb_gui(f"Nexus credits: operation {nexus_op_id} marked {'COMPLETED' if success else 'FAILED'}", "info")
            else:
                log_cb_gui(f"Nexus credits: operation {nexus_op_id} completion call failed", "warning")
        except Exception as e:
            log_cb_gui(f"Nexus credits: error completing operation {nexus_op_id}: {e}", "warning")

    result_cb_main_gui(drive_path, final)


def process_wipe_queue(
    drives_to_wipe: List[str],
    method_name: str,
    all_drives_info: List[Dict[str, Any]],
    progress_cb_gui,
    log_cb_gui,
    result_cb_main_gui,
    all_done_cb_gui,
):
    total = len(drives_to_wipe)
    for idx, d in enumerate(drives_to_wipe, 1):
        drive_info = next((x for x in all_drives_info if x["path"] == d), {})
        progress_cb_gui(d, (idx - 1) / total * 100, 0, "starting")
        perform_single_wipe_threaded(
            d,
            method_name,
            drive_info,
            lambda cur, _ov, prog, msg: progress_cb_gui(cur, ((idx - 1) / total * 100) + prog / total, prog, msg),
            log_cb_gui,
            result_cb_main_gui,
        )
    all_done_cb_gui()


# ---------------------------------------------------------------------------
# Test entry-points
# ---------------------------------------------------------------------------

@test_framework.test(
    category=TestCategory.WIPE,
    severity=TestSeverity.CRITICAL,
    description="Interactive secure wipe using GUI and background threads",
)
@test(category=TestCategory.STORAGE, severity=TestSeverity.CRITICAL, 
      name="Secure Drive Wipe", 
      description="Securely wipes selected drives using various methods. DESTRUCTIVE TEST - DATA WILL BE PERMANENTLY ERASED.")
def run_secure_wipe_test(parent_window=None, log_callback=None, **kwargs):
    """Entry-point called by main_window when user checks **Secure Drive Wipe**.
    
    Opens DriveWipeWindow and blocks until the window is closed. Returns a
    simple summary; per-drive details are handled via callbacks into the main
    GUI logger.
    """

    if log_callback is None:
        log_callback = lambda msg, lvl="info": None

    # Collect detailed drive list using drive_info
    from agent.hardware.drive_info import get_detailed_drive_info

    drives_info = get_detailed_drive_info()

    def start_actual_wipe(drives_selected, method_key, prog_cb, log_cb, result_cb, all_done_cb):
        t = threading.Thread(
            target=process_wipe_queue,
            args=(
                drives_selected,
                method_key,
                drives_info,
                prog_cb,
                log_cb,
                result_cb,
                all_done_cb,
            ),
            daemon=True,
        )
        t.start()

    wipe_win = DriveWipeWindow(parent_window, drives_info, start_actual_wipe, log_callback)
    parent_window.wait_window(wipe_win)

    # Retrieve the collected results from the DriveWipeWindow instance
    detailed_results = wipe_win.wipe_results_collected
    
    # Log how many results were collected for debugging or info
    if log_callback:
        log_callback(f"Retrieved {len(detailed_results)} detailed wipe results from DriveWipeWindow.", "info")

    return detailed_results


# Function for wipe verification test - platform specific
if platform.system() == "Linux":
    @test(category=TestCategory.STORAGE, severity=TestSeverity.MEDIUM,
          name="Drive Wipe Verification",
          description="Verifies that drives have been properly wiped by checking for zero patterns.")
    def run_wipe_verification_test(log_callback=None, **kwargs):
        """Verify that drives have been properly wiped by checking for zero patterns.
        
        This test is Linux-specific and requires root privileges.
        """
        if log_callback is None:
            def log_callback(msg, level="info"):
                print(f"[{level.upper()}] {msg}")
                
        try:
            # Ensure we're running as root
            if hasattr(os, "geteuid") and os.geteuid() != 0:
                log_callback("Drive wipe verification requires root privileges", "error")
                return {"test_details": {"status": TestStatus.FAIL.value, "notes": "Root privileges required"}}
                
            # List available drives
            drives = []
            try:
                # Use lsblk to list block devices
                output = subprocess.check_output(["lsblk", "-d", "-o", "NAME,TYPE,SIZE", "-n"], text=True)
                for line in output.strip().split("\n"):
                    parts = line.split()
                    if len(parts) >= 2 and parts[1] == "disk":
                        drives.append(f"/dev/{parts[0]}")
            except Exception as e:
                log_callback(f"Error listing drives: {e}", "error")
                return {"test_details": {"status": TestStatus.FAIL.value, "notes": f"Error listing drives: {e}"}}
                
            if not drives:
                log_callback("No drives found for verification", "warning")
                return {"test_details": {"status": TestStatus.FAIL.value, "notes": "No drives found for verification"}}
                
            # Verify each drive
            drive_results = []
            overall_status = TestStatus.PASS.value
            
            for drive in drives:
                log_callback(f"Verifying drive {drive}")
                try:
                    # Read the first 1MB of the drive and check if it's all zeros
                    result = subprocess.run(
                        ["dd", f"if={drive}", "bs=1M", "count=1"],
                        capture_output=True,
                        check=True
                    )
                    
                    # Calculate SHA-256 of the data
                    data_hash = hashlib.sha256(result.stdout).hexdigest()
                    
                    # Check if the hash matches the hash of 1MB of zeros
                    if data_hash == ZERO_MB_SHA256:
                        status = TestStatus.PASS.value
                        notes = f"Drive {drive} verified as wiped (zeros)"
                    else:
                        status = TestStatus.FAIL.value
                        notes = f"Drive {drive} may not be properly wiped"
                        overall_status = TestStatus.FAIL.value
                        
                except Exception as e:
                    status = TestStatus.ERROR.value
                    notes = f"Error verifying drive {drive}: {e}"
                    overall_status = TestStatus.FAIL.value
                    
                drive_results.append({"drive": drive, "status": status, "notes": notes})
                log_callback(notes, "success" if status == TestStatus.PASS.value else "error")
                
            return {
                "test_details": {
                    "status": overall_status,
                    "notes": f"Verified {len(drives)} drives",
                    "drive_results": drive_results
                }
            }
            
        except Exception as e:
            log_callback(f"Error in wipe verification test: {e}", "error")
            return {"test_details": {"status": TestStatus.ERROR.value, "notes": f"Error: {e}"}}
            
else:
    # On non-Linux platforms, provide a stub that returns a skipped status
    @test(category=TestCategory.STORAGE, severity=TestSeverity.MEDIUM,
          name="Drive Wipe Verification",
          description="Verifies that drives have been properly wiped by checking for zero patterns. Linux only.")
    def run_wipe_verification_test(log_callback=None, **kwargs):
        """Stub for drive wipe verification on non-Linux platforms.
        
        This test is only available on Linux.
        """
        if log_callback is None:
            def log_callback(msg, level="info"):
                print(f"[{level.upper()}] {msg}")
                
        log_callback("Drive wipe verification is only available on Linux", "warning")
        return {
            "test_details": {
                "status": TestStatus.SKIPPED.value,
                "notes": "Drive wipe verification is only available on Linux"
            }
        }
