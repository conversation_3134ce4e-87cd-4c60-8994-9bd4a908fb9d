# Nexus Standalone Product Plan (Linux-only v1)

This document defines how we will ship Nexus as a downloadable, installable product that can PXE-boot laptops/PCs to a custom Debian image that hosts Crucible.

## Objectives
- Deliver a turnkey Linux-only v1.
- Enable PXE out-of-the-box: Nexus provides HTTP iPXE endpoints and optional PXE handoff (dnsmasq) on Linux.
- Zero customer .env edits or CLI usage.
- Professional distribution: OVA appliance primary; Compose bundle secondary.

## SKUs (v1 scope)
- Appliance (OVA/OVF): Primary distribution for Proxmox/VMware/VirtualBox.
- Linux Server Bundle (Docker Compose): Secondary option with installer scripts.

## High-level Architecture
- UI: Next.js app (`Nexus/ui/`), served via Nginx in production.
- API: FastAPI (`Nexus/api/`), containerized.
- Reverse Proxy: Nginx (`Nexus/infra/nginx.conf`).
- PXE Handoff (Linux-only): dnsmasq container (ProxyDHCP/TFTP), optional but enabled by default in lab setup.
- Artifacts: `Nexus/artifacts/` served at `/artifacts`.
- iPXE Script: `GET /ipxe` (`Nexus/api/app/routers/ipxe.py`).
- Config Store: persisted `nexus-config.json` in a Docker volume (no user .env).

## Customer Experience
1) Download OVA (preferred) or Compose bundle from the portal.
2) Power on (OVA) or run `install.sh` (bundle). Browser opens to `/setup`.
3) Setup Wizard collects:
   - License (online activation or upload license file).
   - Mode: Quickstart (SQLite) or Production (Postgres).
   - Network/PXE: NIC selection, ProxyDHCP vs Authoritative, TFTP on/off, HTTPBoot info.
   - Domain/TLS: Let’s Encrypt or upload certs (fallback to self-signed until DNS ready).
   - Admin user.
4) Wizard writes config, generates secrets, applies Nginx certs, starts services.
5) Redirect to Nexus login; PXE-ready.

## PXE Design (Linux-only)
- Modes:
  - ProxyDHCP (default): Coexists with existing DHCP server; safe for office networks.
  - Authoritative DHCP (opt-in): For isolated lab switch; Nexus provides leases.
- Firmware:
  - UEFI x64 first (`ipxe.efi`), Legacy BIOS fallback (`undionly.kpxe`).
  - UEFI HTTPBoot supported (no TFTP).
- iPXE chain target: `http://<nexus-host>/ipxe?mac=${net0/mac}&uuid=${uuid}`.
- TFTP root: `/var/lib/nexus/tftp` (volume). Contains `ipxe.efi`, `undionly.kpxe`, and `default.ipxe` (chains to `/ipxe`).
- dnsmasq runs with host networking on Linux. Generated config from wizard guards against network disruption.

## Files and Layout
- `Nexus/api/` – FastAPI, `Dockerfile` already present.
- `Nexus/ui/` – Next.js (add production `Dockerfile`).
- `Nexus/infra/`
  - `nginx.conf` – reverse proxy rules (add `/setup` gating).
  - `compose/quickstart/docker-compose.yml` – SQLite, optional PXE.
  - `compose/prod/docker-compose.yml` – Postgres, optional PXE, health checks.
  - `.env.template` – internal use; not exposed to customers (wizard writes config).
- `Nexus/artifacts/` – kernel/initrd/ISO served at `/artifacts`.
- `Nexus/docs/` – this plan and customer-facing guides.

## Setup Wizard
- Minimal service available at `/setup` until completion.
- Responsibilities:
  - Validate one-time setup token (embedded in download) or manual license entry.
  - Generate secrets; write `nexus-config.json` to a volume (and Docker/K8s secrets if applicable).
  - Configure Nginx (domain/TLS) and request Let’s Encrypt if chosen.
  - Create admin account; run DB migrations; enable API/UI; optionally enable dnsmasq.
  - Disable `/setup` after completion.

## Config Model (no customer .env)
- Persisted at `/var/lib/nexus/config/nexus-config.json` (mounted into API and setup services):
  - portalBaseUrl
  - license: { mode: online|offline, key|file }
  - database: { driver: sqlite|postgres, url, creds }
  - domain/tls: { mode: le|custom|selfsigned, email?, certPaths? }
  - pxe: { mode: proxy|authoritative|off, interface, tftp: on|off }
  - admin: { email, passwordHash } (password only during setup)
  - generatedSecrets: { jwtSecret, sessionKeys, dbPass? }

## Production Compose (summary)
- Services: api, ui, nginx, postgres (prod mode), pxe-dnsmasq (optional), setup (first-run only).
- Volumes: db, config, artifacts, tftp, certs.
- Health checks: `/api/health`, nginx `:80`/`:443`.
- No dev mounts or `--reload`.

## Security
- TLS: Let’s Encrypt by default; custom cert upload supported; self-signed fallback.
- Image security: SBOM (Syft), scan (Trivy), cosign sign images.
- Secrets: generated on first boot; stored privately; never shown post-setup.
- Guardrails: explicit confirmation before enabling authoritative DHCP.

## Licensing & Portal Integration
- Online activation via portal APIs (`/api/licenses/status`, `/api/licenses/activate`).
- Offline: upload signed license file (JWT) from portal.
- Installer downloads personalized bundle with one-time setup token to auto-fill org/license.

## Distribution
- OVA/OVF: Built with Packer. Preinstalls Docker/Compose, systemd units, and compose bundle.
- Compose Bundle (tar.gz): Production compose, scripts (`install.sh`, `upgrade.sh`, `uninstall.sh`, `health.sh`), README.

## CI/CD (minimum to ship v1)
- On tag: build API/UI images, test, SBOM, scan, cosign, push to GHCR/ECR.
- Build OVA with Packer and publish artifact + checksums/signature.
- Release page links: OVA, Compose bundle, checksum/signature files.

## Customer Docs (deliverables)
- Install OVA (with screenshots) and first-boot wizard.
- Compose install and network prerequisites.
- PXE modes: ProxyDHCP vs Authoritative, UEFI/BIOS guidance, HTTPBoot.
- TLS and domain setup.
- Upgrades and rollback.
- Troubleshooting and health checks.

## Timeline (aggressive, 2 weeks to first downloadable)
- Week 1:
  - Add `Nexus/ui/Dockerfile`; create production compose (quickstart + prod variants).
  - Implement minimal setup service + config writer; gate `/setup` in Nginx.
  - Add optional `pxe-dnsmasq` service (ProxyDHCP default) and TFTP assets.
- Week 2:
  - Packer OVA build; smoke tests (PXE in lab, UEFI + BIOS paths).
  - CI/CD: image signing and release publishing.
  - Author install/PXE docs; prepare Downloads page content.

## Open Decisions
- Registry: GHCR vs ECR.
- Default TLS mode at first run (self-signed vs HTTP-01 attempt).
- Whether `pxe-dnsmasq` is enabled by default in OVA (likely yes, ProxyDHCP).
- Supported NIC selection UX for multi-NIC hosts.

## Risks & Mitigations
- DHCP conflicts: default to ProxyDHCP, add clear warnings, NIC selection, and an on-screen status page.
- Let’s Encrypt challenges: fall back to self-signed; provide retry from Settings.
- UEFI vs BIOS diversity: ship both NBPs; document HTTPBoot path where possible.
- Air-gapped sites: offer offline license file and offline update packs.
