# Operator ID Communication Between Crucible and Nexus

## Overview

This document describes the complete operator ID communication flow between Crucible (client) and Nexus (server), including the improvements made to ensure reliable transmission and storage of operator information.

## Problem Statement

Previously, operator IDs were not being consistently saved in Nexus when test results were submitted from Crucible clients. This made it difficult to track who performed which tests, which is critical for audit trails and accountability.

## Solution Architecture

### 1. UI Collection (Crucible Frontend)
- **Location**: `/web_server/templates/index.html` (lines 21-22)
- **Implementation**: Operator ID input field with autofocus
- **Validation**: Required field validation in JavaScript

### 2. Frontend Processing (Crucible JavaScript)
- **Location**: `/web_server/static/js/modules/test_execution.js`
- **Implementation**: 
  - Collects operator ID from UI (line 49)
  - Validates presence before test execution (line 82)
  - Includes in payload to backend (line 97)

### 3. Backend Processing (Crucible Python)
- **Location**: `/web_server/app.py`
- **Implementation**:
  - Receives operator ID in `/api/run_tests` endpoint (line 129)
  - Validates required fields including operator ID (line 131)
  - Passes to test orchestrator (line 215)

### 4. Result Management (Enhanced)
- **Location**: `/agent/core/result_manager.py`
- **Implementation**:
  - Uses new centralized Nexus client (line 105)
  - Robust operator ID handling with fallbacks
  - Multiple field name support for compatibility

### 5. Centralized Nexus Client (New)
- **Location**: `/agent/core/nexus_client.py`
- **Features**:
  - Retry logic with exponential backoff
  - Multiple operator field names (`operator`, `operator_id`, `operatorId`)
  - Comprehensive error handling and logging
  - Batch submission support
  - MAC address auto-detection

### 6. Nexus API (Server)
- **Location**: `/home/<USER>/dev/Nexus-Hub/api/app/routers/results.py`
- **Implementation**:
  - Robust fallback logic for operator field names (lines 28-36)
  - Database storage with proper indexing
  - Query support for operator-based filtering

## Data Flow

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│   Crucible  │    │   Crucible   │    │   Crucible  │    │    Nexus    │
│     UI      │───▶│   Backend    │───▶│   Nexus     │───▶│  Database   │
│             │    │              │    │   Client    │    │             │
└─────────────┘    └──────────────┘    └─────────────┘    └─────────────┘
     │                      │                  │                  │
     │ operator_id          │ operator_id      │ operator         │ operator
     │ asset_number         │ asset_number     │ operator_id      │ asset_number
     │ profile_name         │ profile_name     │ operatorId       │ test_name
     │                      │                  │ asset_number     │ status
     │                      │                  │ test_name        │ ...
     │                      │                  │ status           │
```

## Key Improvements

### 1. Centralized Communication
- **New Module**: `agent/core/nexus_client.py`
- **Benefits**: 
  - Single point of configuration
  - Consistent error handling
  - Retry logic for network issues
  - Standardized logging

### 2. Enhanced Reliability
- **Multiple Field Names**: Supports `operator`, `operator_id`, and `operatorId`
- **Fallback Logic**: Environment variables and previous results
- **Validation**: Required field checking at multiple levels
- **Retry Logic**: Automatic retry with exponential backoff

### 3. Better Debugging
- **Comprehensive Logging**: Detailed logs at each step
- **Test Script**: `test_operator_flow.py` for validation
- **Status Tracking**: Clear success/failure indicators

### 4. Backward Compatibility
- **Gradual Migration**: Old ResultManager still works
- **Multiple Field Support**: Handles various client implementations
- **Environment Variables**: Configuration flexibility

## Configuration

### Environment Variables

```bash
# Nexus connection
NEXUS_BASE_URL=http://localhost:8080
NEXUS_TIMEOUT=10
NEXUS_MAX_RETRIES=3
NEXUS_RETRY_DELAY=1.0

# Default values
NEXUS_OPERATOR=default_operator
NEXUS_PROFILE_NAME=default_profile
NEXUS_MAC=auto_detect
```

### Database Schema

The Nexus database includes the `operator` field:

```sql
ALTER TABLE results ADD COLUMN operator VARCHAR(128);
CREATE INDEX IF NOT EXISTS ix_results_operator ON results(operator);
```

## Testing

### Automated Tests
Run the test script to verify the complete flow:

```bash
cd /home/<USER>/dev/Crucible
python test_operator_flow.py
```

### Manual Testing
1. Start Nexus server
2. Start Crucible web interface
3. Enter operator ID and asset number
4. Select test profile and run tests
5. Verify operator ID appears in Nexus results

## API Endpoints

### Crucible → Nexus
- **Endpoint**: `POST /api/results`
- **Payload**:
```json
{
  "asset_number": "string",
  "operator": "string",
  "operator_id": "string",
  "operatorId": "string",
  "test_name": "string",
  "status": "passed|failed|skipped|running",
  "metrics": {},
  "profile_name": "string",
  "mac": "string"
}
```

### Nexus Query
- **Endpoint**: `GET /api/results?operator=value`
- **Response**: Array of results filtered by operator

## Troubleshooting

### Common Issues

1. **Operator ID Not Saved**
   - Check Nexus database migration completed
   - Verify network connectivity between Crucible and Nexus
   - Check logs for submission errors

2. **Empty Operator Field**
   - Ensure UI validation is working
   - Check JavaScript console for errors
   - Verify backend receives operator_id

3. **Network Issues**
   - Check NEXUS_BASE_URL configuration
   - Verify Nexus server is running
   - Check firewall/network connectivity

### Debugging Commands

```bash
# Check Nexus connectivity
curl -X GET http://localhost:8080/api/results?limit=1

# Check recent results with operator
curl -X GET "http://localhost:8080/api/results?operator=test_operator"

# View Crucible logs
tail -f /var/log/crucible/app.log

# Test operator flow
python /home/<USER>/dev/Crucible/test_operator_flow.py
```

## Future Enhancements

1. **Real-time Validation**: Live operator ID validation against Nexus
2. **Operator Profiles**: Store operator information and preferences
3. **Audit Trail**: Enhanced logging for compliance requirements
4. **Batch Operations**: Bulk operator ID updates for historical data
5. **Integration**: SSO integration for automatic operator detection

## Conclusion

The operator ID communication system is now robust and reliable, with comprehensive error handling, retry logic, and multiple fallback mechanisms. The centralized Nexus client provides a clean interface for all future communication needs, making the system more maintainable and extensible.
