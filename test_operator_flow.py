#!/usr/bin/env python3
"""
Test script to verify operator ID transmission from Crucible to Nexus.
This script simulates the complete flow and validates that operator IDs are properly transmitted.
"""

import os
import sys
import json
import time
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from agent.core.nexus_client import NexusClient, TestResult, get_nexus_client
from agent.core.result_manager import ResultManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_nexus_connectivity():
    """Test if Nexus is reachable."""
    print("🔍 Testing Nexus connectivity...")
    client = get_nexus_client()
    
    if client.check_connectivity():
        print("✅ Nexus is reachable")
        return True
    else:
        print("❌ Nexus is not reachable")
        return False

def test_direct_nexus_submission():
    """Test direct submission to Nexus using the new client."""
    print("\n🧪 Testing direct Nexus submission...")
    
    client = get_nexus_client()
    
    test_result = TestResult(
        asset_number="TEST_ASSET_001",
        operator_id="TEST_OPERATOR_123",
        test_name="Operator ID Test",
        status="passed",
        profile_name="Test Profile",
        metrics={"test_metric": "test_value"}
    )
    
    success = client.submit_test_result(test_result)
    
    if success:
        print("✅ Direct Nexus submission successful")
        return True
    else:
        print("❌ Direct Nexus submission failed")
        return False

def test_result_manager_flow():
    """Test the ResultManager flow with operator ID."""
    print("\n🔄 Testing ResultManager flow...")
    
    def mock_log(message, level='info'):
        print(f"LOG [{level.upper()}]: {message}")
    
    result_manager = ResultManager(log_callback=mock_log)
    
    # Test with a typical test result structure
    sample_result = {
        'status': 'pass',
        'grade': 'A',
        'notes': 'Operator ID transmission test',
        'metrics': {
            'test_duration': 5.2,
            'operator_verified': True
        },
        'started_at': time.time() - 10,
        'finished_at': time.time()
    }
    
    result_manager.add_result(
        asset_number="TEST_ASSET_002",
        operator_id="TEST_OPERATOR_456", 
        test_name="ResultManager Operator Test",
        result=sample_result,
        profile_name="Test Profile"
    )
    
    print("✅ ResultManager flow completed")
    return True

def test_batch_submission():
    """Test batch submission with multiple results."""
    print("\n📦 Testing batch submission...")
    
    client = get_nexus_client()
    
    results = [
        TestResult(
            asset_number="BATCH_ASSET_001",
            operator_id="BATCH_OPERATOR_001",
            test_name="Batch Test 1",
            status="passed"
        ),
        TestResult(
            asset_number="BATCH_ASSET_001", 
            operator_id="BATCH_OPERATOR_001",
            test_name="Batch Test 2",
            status="failed"
        ),
        TestResult(
            asset_number="BATCH_ASSET_001",
            operator_id="BATCH_OPERATOR_001", 
            test_name="Batch Test 3",
            status="passed"
        )
    ]
    
    success = client.submit_batch_results(results)
    
    if success:
        print("✅ Batch submission successful")
        return True
    else:
        print("❌ Batch submission failed")
        return False

def verify_results_in_nexus():
    """Verify that submitted results appear in Nexus with operator IDs."""
    print("\n🔍 Verifying results in Nexus...")
    
    client = get_nexus_client()
    
    # Check for our test results
    test_operators = ["TEST_OPERATOR_123", "TEST_OPERATOR_456", "BATCH_OPERATOR_001"]
    
    for operator in test_operators:
        results = client.get_results(operator=operator, limit=10)
        
        if results:
            print(f"✅ Found {len(results)} results for operator {operator}")
            for result in results[:3]:  # Show first 3 results
                print(f"   - {result.get('test_name', 'Unknown')} | {result.get('status', 'Unknown')} | Asset: {result.get('asset_number', 'Unknown')}")
        else:
            print(f"⚠️  No results found for operator {operator}")
    
    return True

def main():
    """Run all operator ID flow tests."""
    print("🚀 Starting Operator ID Flow Tests")
    print("=" * 50)
    
    # Set test environment
    os.environ['NEXUS_BASE_URL'] = os.environ.get('NEXUS_BASE_URL', 'http://localhost:8080')
    
    tests = [
        ("Nexus Connectivity", test_nexus_connectivity),
        ("Direct Nexus Submission", test_direct_nexus_submission),
        ("ResultManager Flow", test_result_manager_flow),
        ("Batch Submission", test_batch_submission),
        ("Verify Results", verify_results_in_nexus)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All operator ID flow tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check Nexus connectivity and configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
