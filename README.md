# Nexus (PXE Hub) – MVP (Relay mode)

Nexus lets you host iPXE scripts and boot artifacts (kernel/initrd/ISO) and view/manage devices running Crucible. This MVP runs locally with Docker Compose.

- UI: Next.js (JavaScript only, no TypeScript)
- API: FastAPI (Python)
- Reverse proxy + static: Nginx
- DB: SQLite (file in `infra/data/`)

## Prereqs (Fedora)
- Docker Engine + Compose plugin installed
- Map `nexus.lan` in DNS or use `http://localhost:8080` for UI while developing. The iPXE script uses `nexus.lan:8080` by default (configurable via env `NEXUS_HOSTNAME`).

## Run (development)

From this folder:

```bash
cd infra
docker compose up --build
```

Services:
- UI: http://localhost:8080/
- API (proxied): http://localhost:8080/api/health
- Artifacts: http://localhost:8080/artifacts/
- iPXE script: http://localhost:8080/ipxe?mac=00:11:22:33:44:55&uuid=demo

Stop with Ctrl+C. Add `-d` to run detached.

## Configure PXE settings
1. Open the UI → “PXE Settings” card.
2. Set:
   - Kernel URL (example): `/artifacts/vmlinuz`
   - Initrd URL (example): `/artifacts/initrd.img`
   - Kernel Args (example): `boot=live fetch=http://nexus.lan:8080/artifacts/rootfs.squashfs`
3. Click Save. Visit `/ipxe?mac=...` to see the generated script.

You can serve files by placing them in `Nexus/artifacts/` on the host.

## Relay mode (high level)
- Your existing DHCP chainloads to Nexus iPXE:
  ```ipxe
  chain http://nexus.lan/ipxe?mac=${net0/mac}&uuid=${uuid}
  ```
- Nexus responds with an iPXE script pointing to your configured kernel/initrd and args.

## Project layout
```
Nexus/
  api/               # FastAPI app
  ui/                # Next.js app (JS only)
  artifacts/         # Kernel/initrd/ISO (served at /artifacts)
  infra/
    docker-compose.yml
    nginx.conf
    data/            # SQLite DB file (runtime)
```

## Development notes
- UI dev server runs in a Node container and is reverse-proxied by Nginx. Hot reload works.
- API runs with `--reload` for live code changes.
- iPXE hostname used in scripts is `NEXUS_HOSTNAME` (default `nexus.lan:8080` in compose). Change it if you prefer.
- Keep code modular: small routers/services/schemas. Avoid giant files.

## Status
- [x] Next.js UI scaffold and reverse proxy via Nginx
- [x] FastAPI scaffold with `/api/health`
- [x] Docker Compose for UI/API/Nginx and static artifacts
- [x] Artifacts hosting at `/artifacts`
- [x] iPXE script endpoint `/ipxe`
- [x] Device results ingestion API and live updates (SSE)
- [x] Profiles UI
- [x] Per-MAC overrides influencing iPXE generation
- [ ] Optional: Postgres migration path

## Smoke test
With the stack running:

- Health check
  ```bash
  curl -sS http://localhost:8080/api/health
  ```

- Post a sample result (should appear live on the dashboard “Live Results” card)
  ```bash
  curl -sS -X POST http://localhost:8080/api/results \
    -H 'Content-Type: application/json' \
    -d '{
      "asset_number":"A1",
      "mac":"00:11:22:33:44:55",
      "profile_name":"Default",
      "test_name":"Keyboard Test",
      "status":"passed"
    }'
  ```

- Set a per-MAC iPXE override and fetch script
  ```bash
  curl -sS -X PUT http://localhost:8080/api/device_overrides/00:11:22:33:44:55 \
    -H 'Content-Type: application/json' \
    -d '{
      "mac":"00:11:22:33:44:55",
      "kernel_url":"/artifacts/vmlinuz",
      "initrd_url":"/artifacts/initrd.img",
      "kernel_args":"boot=live fetch=http://nexus.lan:8080/artifacts/rootfs.squashfs"
    }'

  curl -sS "http://localhost:8080/ipxe?mac=00:11:22:33:44:55&uuid=demo"
  ```

## Results & Wipes API

### Post a single result

```bash
curl -sS -X POST http://localhost:8080/api/results \
  -H 'Content-Type: application/json' \
  -d '{
    "asset_number":"A1",
    "mac":"00:11:22:33:44:55",
    "profile_name":"Default",
    "run_id":"11111111-1111-1111-1111-111111111111",
    "category":"diagnostics",
    "test_name":"Keyboard Test",
    "status":"passed",
    "metrics": {"keys_tested": 84},
    "started_at":"2025-08-31T17:00:00Z",
    "ended_at":"2025-08-31T17:00:10Z"
  }'
```

Status must be one of: `passed|failed|skipped|running`.

### Post a batch of results

```bash
curl -sS -X POST http://localhost:8080/api/results/batch \
  -H 'Content-Type: application/json' \
  -d '{
    "run_id": "11111111-1111-1111-1111-111111111111",
    "results": [
      {"asset_number":"A1","mac":"00:11:22:33:44:55","test_name":"CPU Test","status":"passed"},
      {"asset_number":"A1","mac":"00:11:22:33:44:55","test_name":"RAM Test","status":"passed","category":"memory"}
    ]
  }'
```

You can also query results:

- Latest per test for an asset/MAC: `/api/results/latest?asset=A1` or `?mac=00:..`
- All results with optional filters: `/api/results?asset=A1&mac=...&run=...&category=...`
- By run: `/api/results/run/{run_id}`

### Post a wipe and view the certificate

```bash
curl -sS -X POST http://localhost:8080/api/wipes \
  -H 'Content-Type: application/json' \
  -d '{
    "asset_number":"A1",
    "mac":"00:11:22:33:44:55",
    "run_id":"11111111-1111-1111-1111-111111111111",
    "drive_model":"SAMSUNG SSD 860 EVO",
    "drive_serial":"S3Z9NB0K123456X",
    "drive_size_bytes":500107862016,
    "interface":"SATA",
    "method_standard":"NIST SP 800-88 Rev.1 Purge",
    "method_technique":"ata-secure-erase",
    "passes":1,
    "verify_method":"read-sample",
    "verify_result":"passed",
    "status":"passed",
    "operator":"tech01",
    "location":"RMA-BAY-3"
  }'
```

List and filter wipes:

- `/api/wipes?asset=A1` or `?mac=...` or `?run=...` or `?serial=...`
- Get one: `/api/wipes/{id}`
- Certificate (server-rendered HTML): `/api/wipes/{id}/certificate.html`

## Using the Lookup and Certificate Viewer

- Open the UI → “Lookup”. Enter an Asset Number or MAC.
- The page shows:
  - Latest Test Results: newest per test for that asset/MAC.
  - Wipe Reports: matching wipes with a “View” link.
- Clicking “View” opens the embedded certificate page at `/wipes/{id}` with:
  - An iframe of the server-rendered certificate.
  - Buttons to “Open in new tab” and “Print”.

Notes:

- If you run the UI against a remote API, set `NEXT_PUBLIC_API_BASE` in the UI environment. Certificate links and the embedded viewer will adapt to a BASE that includes `/api`.

## Next steps
- Optional: switch DB to Postgres when we need scale/reporting.
