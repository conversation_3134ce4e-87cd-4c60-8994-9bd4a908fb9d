services:
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=nexus
      - POSTGRES_PASSWORD=nexus
      - POSTGRES_DB=nexus
    volumes:
      - nexus-pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nexus"]
      interval: 10s
      timeout: 5s
      retries: 10
    restart: unless-stopped

  nexus-api:
    build: ../../../api
    environment:
      - NEXUS_DB_URL=postgresql+psycopg2://nexus:nexus@postgres:5432/nexus
      - NEXUS_HOSTNAME=nexus.lan
    depends_on:
      postgres:
        condition: service_healthy
    expose:
      - "8000"
    restart: unless-stopped

  nexus-ui:
    build: ../../../ui
    environment:
      - NEXT_PUBLIC_API_BASE=/api
    expose:
      - "3000"
    depends_on:
      - nexus-api
    restart: unless-stopped

  nexus-nginx:
    image: nginx:alpine
    volumes:
      - ../../nginx.conf:/etc/nginx/nginx.conf:ro
      - ../../state:/usr/share/nexus/state:ro,Z
      - ../../../artifacts:/usr/share/nexus/artifacts:ro
    ports:
      - "8080:80"
    depends_on:
      - nexus-api
      - nexus-ui
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost"]
      interval: 10s
      timeout: 3s
      retries: 10
    restart: unless-stopped

volumes:
  nexus-pgdata:
