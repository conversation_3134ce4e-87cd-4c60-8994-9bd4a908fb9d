services:
  nexus-api:
    build: ../../../api
    environment:
      - NEXUS_DB_URL=sqlite:////data/nexus.db
      - NEXUS_HOSTNAME=nexus.lan
    volumes:
      - nexus-data:/data
    expose:
      - "8000"
    restart: unless-stopped

  nexus-ui:
    build: ../../../ui
    environment:
      - NEXT_PUBLIC_API_BASE=/api
    expose:
      - "3000"
    depends_on:
      - nexus-api
    restart: unless-stopped

  nexus-nginx:
    image: nginx:alpine
    volumes:
      - ../../nginx.conf:/etc/nginx/nginx.conf:ro
      - ../../state:/usr/share/nexus/state:ro,Z
      - ../../../artifacts:/usr/share/nexus/artifacts:ro
    ports:
      - "8081:80"
    depends_on:
      - nexus-api
      - nexus-ui
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost"]
      interval: 10s
      timeout: 3s
      retries: 10
    restart: unless-stopped

  # Optional: PXE handoff (Linux-only). Disabled by default.
  # Requires running on a Linux host with host networking to bind DHCP/TFTP ports.
  # pxe-dnsmasq:
  #   image: ghcr.io/your-org/nexus-pxe-dnsmasq:latest
  #   network_mode: host
  #   cap_add:
  #     - NET_ADMIN
  #   volumes:
  #     - nexus-tftp:/var/lib/nexus/tftp
  #     - nexus-config:/var/lib/nexus/config
  #   restart: unless-stopped

volumes:
  nexus-data:
  nexus-tftp:
  nexus-config:
