user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events { worker_connections 1024; }

http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;
  sendfile      on;
  keepalive_timeout  65;

  server {
    listen 80;
    server_name _;

    # Proxy /setup to the UI page
    location = /setup {
      proxy_pass http://nexus-ui:3000/setup;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    # UI (Next.js app)
    location / {
      proxy_pass http://nexus-ui:3000;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
    }

    # API
    location /api/ {
      proxy_pass http://nexus-api:8000/api/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    # iPXE endpoint (short path)
    location = /ipxe {
      proxy_pass http://nexus-api:8000/ipxe;
      proxy_set_header Host $host;
    }
    location /ipxe {
      proxy_pass http://nexus-api:8000/ipxe;
      proxy_set_header Host $host;
    }

    # Artifacts (static files)
    location /artifacts/ {
      alias /usr/share/nexus/artifacts/;
      autoindex on;
      add_header Cache-Control "no-store";
    }
  }
}
