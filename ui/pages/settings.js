import { useEffect, useMemo, useState } from 'react'
import { apiGet, apiPost, apiDelete } from '../src/lib/api'
import Card from '../src/components/Card'
import Layout from '../src/components/Layout'

export default function SettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [activeTab, setActiveTab] = useState('connection') // connection | database | erp

  const [baseUrl, setBaseUrl] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [apiKeySet, setApiKeySet] = useState(false)
  const [showKey, setShowKey] = useState(false)

  const [dbMode, setDbMode] = useState('local')
  const [dbUrl, setDbUrl] = useState('')
  const [dbTimezone, setDbTimezone] = useState('America/Chicago')
  const [dbRetentionDays, setDbRetentionDays] = useState(90)
  const [dbSaving, setDbSaving] = useState(false)
  const [dbError, setDbError] = useState('')
  const [dbSuccess, setDbSuccess] = useState('')
  const [clearBusy, setClearBusy] = useState(false)
  const [clearMsg, setClearMsg] = useState('')
  const [clearErr, setClearErr] = useState('')

  const urlValid = useMemo(() => {
    if (!baseUrl) return true
    try {
      const u = new URL(baseUrl)
      return u.protocol === 'https:' || u.hostname === 'localhost'
    } catch { return false }
  }, [baseUrl])

  useEffect(() => {
    let mounted = true
    async function load() {
      setLoading(true)
      setError('')
      try {
        const [websiteData, dbData] = await Promise.all([
          apiGet('/api/settings/website'),
          apiGet('/api/settings/database')
        ])
        if (!mounted) return
        setBaseUrl(websiteData.base_url || '')
        setApiKey('') // never prefill key
        setApiKeySet(Boolean(websiteData.api_key_set))
        setDbMode(dbData.db_mode || 'local')
        setDbUrl(dbData.db_url || '')
        setDbTimezone(dbData.timezone || 'America/Chicago')
        setDbRetentionDays(dbData.retention_days || 90)
      } catch (e) {
        setError(String(e.message || e))
      } finally {
        setLoading(false)
      }
    }
    load()
    return () => { mounted = false }
  }, [])

  async function onSubmit(e) {
    e.preventDefault()
    if (!urlValid) {
      setError('Please enter a valid URL (https recommended).')
      return
    }
    setSaving(true)
    setSuccess('')
    setError('')
    try {
      const payload = { base_url: baseUrl || null }
      if (apiKey && apiKey.trim()) payload.api_key = apiKey.trim()
      const data = await apiPost('/api/settings/website', payload)
      setApiKey('') // clear after save
      setApiKeySet(Boolean(data.api_key_set))
      setSuccess('Settings saved')
    } catch (e) {
      setError(String(e.message || e))
    } finally {
      setSaving(false)
    }
  }

  async function onDbSubmit(e) {
    e.preventDefault()
    if (dbMode === 'external' && !dbUrl.trim()) {
      setDbError('DB URL is required for external mode.')
      return
    }
    setDbSaving(true)
    setDbSuccess('')
    setDbError('')
    try {
      const payload = {
        db_mode: dbMode,
        db_url: dbMode === 'external' ? dbUrl.trim() : null,
        timezone: dbTimezone,
        retention_days: dbRetentionDays
      }
      const data = await apiPost('/api/settings/database', payload)
      setDbMode(data.db_mode || 'local')
      setDbUrl(data.db_url || '')
      setDbTimezone(data.timezone || 'America/Chicago')
      setDbRetentionDays(data.retention_days || 90)
      setDbSuccess('Database settings saved')
    } catch (e) {
      setDbError(String(e.message || e))
    } finally {
      setDbSaving(false)
    }
  }

  async function onClearDb() {
    setClearErr('')
    setClearMsg('')
    const sure = window.confirm('This will permanently delete all local operational data (devices, sessions, results, wipes). Settings are preserved. Continue?')
    if (!sure) return
    setClearBusy(true)
    try {
      const res = await apiDelete('/api/settings/database/clear')
      const counts = res?.counts || {}
      const parts = Object.entries(counts).map(([k, v]) => `${k}: ${v}`).join(', ')
      setClearMsg(`Cleared. ${parts || 'No rows deleted.'}`)
    } catch (e) {
      setClearErr(String(e.message || e))
    } finally {
      setClearBusy(false)
    }
  }

  return (
    <Layout>
      <div style={{ maxWidth: 1120, margin: '2rem auto', padding: '1rem' }}>
        <div style={{ display:'flex', justifyContent:'space-between', alignItems:'center', gap:12 }}>
          <div>
            <h1>Settings</h1>
            <p>Organize Nexus Hub configuration by category.</p>
          </div>
          <a className="btn-secondary" href="/">← Back to Home</a>
        </div>

        {/* Tabs */}
        <div className="tabs">
          <button
            className={`tab ${activeTab === 'connection' ? 'active' : ''}`}
            type="button"
            onClick={() => setActiveTab('connection')}
          >Connection</button>
          <button
            className={`tab ${activeTab === 'database' ? 'active' : ''}`}
            type="button"
            onClick={() => setActiveTab('database')}
          >Database</button>
          <button
            className={`tab ${activeTab === 'erp' ? 'active' : ''}`}
            type="button"
            onClick={() => setActiveTab('erp')}
          >ERP</button>
        </div>

        {loading ? (
          <p>Loading…</p>
        ) : (
          <>
            {activeTab === 'connection' && (
              <div className="grid" style={{ display:'grid', gridTemplateColumns: 'minmax(640px, 1fr) 420px', gap: 24 }}>
                <Card title="Nexus Website Connection">
                  <form onSubmit={onSubmit} className="form">
                    {error ? (
                      <div className="alert error">{error}</div>
                    ) : null}
                    {success ? (
                      <div className="alert success">{success}</div>
                    ) : null}

                    <label>
                      <span>Website Base URL</span>
                      <input
                        id="baseUrl"
                        type="url"
                        placeholder="https://nexus-crucible.vercel.app"
                        value={baseUrl}
                        onChange={(e) => setBaseUrl(e.target.value)}
                        className={!urlValid ? 'invalid' : ''}
                      />
                      <small className="hint">No trailing slash. Example: https://nexus-crucible.vercel.app</small>
                      {!urlValid && <small className="error">Invalid URL. Use HTTPS in production.</small>}
                    </label>

                    <label>
                      <span>API Key</span>
                      <div className="row">
                        <input
                          id="apiKey"
                          type={showKey ? 'text' : 'password'}
                          placeholder={apiKeySet ? 'Stored key is hidden. Enter a new key to replace it.' : 'Paste API key here'}
                          value={apiKey}
                          onChange={(e) => setApiKey(e.target.value)}
                        />
                        <button
                          type="button"
                          className="btn-secondary toggle-btn"
                          aria-pressed={showKey}
                          title={apiKey ? (showKey ? 'Hide key' : 'Show key') : 'Enter a key to enable'}
                          disabled={!apiKey}
                          onClick={() => apiKey && setShowKey(v => !v)}
                        >{showKey ? 'Hide' : 'Show'}</button>
                      </div>
                      <small className="hint">{apiKeySet ? 'A key is configured but cannot be displayed. Enter a new key to replace it.' : 'No API key is configured yet.'}</small>
                    </label>

                    <div className="actions">
                      <button className="btn-secondary" type="submit" disabled={saving || !urlValid}>{saving ? 'Saving…' : 'Save Settings'}</button>
                    </div>
                  </form>
                </Card>

                <Card title="Help">
                  <div className="help">
                    <dl className="meta">
                      <dt>Status</dt>
                      <dd><strong>{apiKeySet ? 'Connected (key set)' : 'Not connected'}</strong></dd>
                      <dt>Website</dt>
                      <dd className="mono">{baseUrl || 'Not set'}</dd>
                    </dl>
                    <div className="actions-row">
                      <button
                        type="button"
                        className="btn-secondary"
                        disabled={!baseUrl}
                        onClick={() => { if (baseUrl) window.open(baseUrl, '_blank', 'noopener,noreferrer') }}
                      >Open Website</button>
                      <button
                        type="button"
                        className="btn-secondary"
                        disabled={!baseUrl}
                        onClick={async () => { try { if (baseUrl) await navigator.clipboard.writeText(baseUrl) } catch {} }}
                      >Copy URL</button>
                    </div>
                    <div className="sep" />
                    <ul className="list">
                      <li>
                        <div className="q">Where do I get an API key?</div>
                        <div className="a">From your Nexus Website admin → Account → API Keys.</div>
                      </li>
                      <li>
                        <div className="q">What does the key do?</div>
                        <div className="a">Allows this hub to consume and complete credit operations during device wiping/diagnostics.</div>
                      </li>
                      <li>
                        <div className="q">Safe to rotate keys?</div>
                        <div className="a">Yes. Entering a new key here replaces the existing one.</div>
                      </li>
                    </ul>
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'database' && (
              <div style={{ maxWidth: 1120, margin: '1rem auto' }}>
                <Card title="Database Configuration">
                  <form onSubmit={onDbSubmit} className="form">
                    {dbError ? (
                      <div className="alert error">{dbError}</div>
                    ) : null}
                    {dbSuccess ? (
                      <div className="alert success">{dbSuccess}</div>
                    ) : null}

                    <label>
                      <span>Database Mode</span>
                      <div className="radio-group">
                        <label className="radio">
                          <input
                            type="radio"
                            name="dbMode"
                            value="local"
                            checked={dbMode === 'local'}
                            onChange={(e) => setDbMode(e.target.value)}
                          />
                          <span>Local SQLite (create own DB)</span>
                        </label>
                        <label className="radio">
                          <input
                            type="radio"
                            name="dbMode"
                            value="external"
                            checked={dbMode === 'external'}
                            onChange={(e) => setDbMode(e.target.value)}
                          />
                          <span>Use Existing Database (external)</span>
                        </label>
                      </div>
                      <small className="hint">Choose whether to use a local SQLite database or connect to an existing external database.</small>
                    </label>

                    {dbMode === 'external' && (
                      <label>
                        <span>Database URL</span>
                        <input
                          id="dbUrl"
                          type="text"
                          placeholder="********************************/db"
                          value={dbUrl}
                          onChange={(e) => setDbUrl(e.target.value)}
                        />
                        <small className="hint">Enter the full database connection URL for the external database.</small>
                      </label>
                    )}

                    <label>
                      <span>Timezone</span>
                      <select
                        value={dbTimezone}
                        onChange={(e) => setDbTimezone(e.target.value)}
                      >
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="America/Anchorage">Alaska Time</option>
                        <option value="Pacific/Honolulu">Hawaii Time (HT)</option>
                        <option value="UTC">UTC</option>
                      </select>
                      <small className="hint">This timezone will be used for displaying dates and timestamps throughout the application.</small>
                    </label>

                    <label>
                      <span>Data Retention (Days)</span>
                      <select
                        value={dbRetentionDays}
                        onChange={(e) => setDbRetentionDays(Number(e.target.value))}
                      >
                        <option value={0}>Keep Forever</option>
                        <option value={7}>7 days</option>
                        <option value={14}>14 days</option>
                        <option value={30}>30 days</option>
                        <option value={60}>60 days</option>
                        <option value={90}>90 days</option>
                        <option value={180}>180 days (6 months)</option>
                        <option value={365}>365 days (1 year)</option>
                      </select>
                      <small className="hint">Automatically delete records older than this many days. Set to 0 to keep records forever.</small>
                    </label>

                    <div className="actions">
                      <button className="btn-secondary" type="submit" disabled={dbSaving}>{dbSaving ? 'Saving…' : 'Save Database Settings'}</button>
                    </div>
                  </form>
                </Card>
                <Card title="Danger zone">
                  {clearErr ? <div className="alert error">{clearErr}</div> : null}
                  {clearMsg ? <div className="alert success">{clearMsg}</div> : null}
                  <p>Clear all operational data. This deletes devices, sessions, results, and wipes. It does not remove settings or profiles.</p>
                  <div className="actions-row">
                    <button type="button" className="btn-secondary" onClick={onClearDb} disabled={clearBusy}>
                      {clearBusy ? 'Clearing…' : 'Clear Database'}
                    </button>
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'erp' && (
              <div style={{ maxWidth: 1120, margin: '1rem auto' }}>
                <Card title="ERP Integration">
                  <div className="help">
                    <p>Configure integration with your ERP system. This section is a placeholder for future settings such as credentials, endpoints, and mapping options.</p>
                    <ul className="list">
                      <li>Supported ERPs: coming soon.</li>
                      <li>Let us know which ERP you use to prioritize support.</li>
                    </ul>
                  </div>
                </Card>
              </div>
            )}
          </>
        )}

      <style jsx>{`
        .form { display:flex; flex-direction:column; gap:14px; }
        label { display:flex; flex-direction:column; gap:6px; }
        input { width:100%; padding:10px; border-radius:8px; border:1px solid var(--border); background:#0f1521; color:var(--text); }
        input.invalid { border-color: #b91c1c; }
        .row { display:flex; gap:8px; align-items:stretch; }
        .row input { flex:1; }
        .toggle-btn { height: 38px; display:inline-flex; align-items:center; }
        .actions { margin-top:6px; }
        .actions-row { display:flex; gap:8px; }
        .alert { padding:10px; border-radius:8px; margin-bottom:8px; }
        .alert.error { background:#2a0b0b; color:#fca5a5; border:1px solid #7f1d1d; }
        .alert.success { background:#0b2a18; color:#86efac; border:1px solid #14532d; }
        .hint { color: var(--muted); }
        .help { display:flex; flex-direction:column; gap:16px; font-size:15px; line-height:1.7; }
        .help .meta { display:grid; grid-template-columns: auto 1fr; column-gap:12px; row-gap:6px; margin:0; }
        .help .meta dt { color: var(--muted); }
        .help .meta dd { margin:0; }
        .help .mono { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; word-break: break-all; }
        .help .sep { height:1px; background: var(--border); margin: 6px 0; }
        .help .list { margin:0; padding-left: 18px; }
        .help .list li { margin-bottom:12px; }
        .help .q { font-weight:600; }
        .radio-group { display:flex; flex-direction:column; gap:8px; }
        .radio { display:flex; align-items:center; gap:8px; cursor:pointer; }
        .radio input[type="radio"] { margin:0; }
        .tabs { display:flex; gap:8px; margin: 16px 0 8px; border-bottom:1px solid var(--border); }
        .tab { appearance:none; background:none; color:var(--muted); border:none; padding:10px 12px; cursor:pointer; border-bottom:2px solid transparent; font-weight:600; }
        .tab:hover { color: var(--text); }
        .tab.active { color: var(--text); border-bottom-color: var(--accent, #4f46e5); }
        `}</style>
      </div>
    </Layout>
  )
}
