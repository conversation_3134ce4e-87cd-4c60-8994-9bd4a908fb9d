import { useEffect, useRef, useState } from 'react'
import useSWR from 'swr'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'
import { apiGet } from '../src/lib/api'

export default function LiveResultsPage() {
  const [liveResults, setLiveResults] = useState([])
  const [filteredResults, setFilteredResults] = useState([])
  const esRef = useRef(null)

  // Get today's date in YYYY-MM-DD format (local timezone)
  // Also get tomorrow's date to handle UTC/local timezone differences
  const getTodayDate = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  const getTomorrowDate = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const year = tomorrow.getFullYear()
    const month = String(tomorrow.getMonth() + 1).padStart(2, '0')
    const day = String(tomorrow.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // Filters state - default to today's results (include tomorrow due to UTC storage)
  const [filters, setFilters] = useState({
    asset: '',
    mac: '',
    status: '',
    test: '',
    dateFrom: getTodayDate(),
    dateTo: getTomorrowDate()
  })
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const resultsPerPage = 50

  // Fetch stats and historical data
  const { data: stats } = useSWR('/api/results/stats', apiGet)

  // Build query string for filters
  const queryString = Object.entries(filters).reduce((acc, [key, value]) => {
    if (value && value.trim()) {
      // Convert date fields to proper format
      if (key === 'dateFrom' && value) {
        acc.push(`start_date=${new Date(value).toISOString().split('T')[0]}`)
      } else if (key === 'dateTo' && value) {
        acc.push(`end_date=${new Date(value).toISOString().split('T')[0]}`)
      } else if (key === 'asset') {
        acc.push(`asset=${encodeURIComponent(value)}`)
      } else if (key === 'mac') {
        acc.push(`mac=${encodeURIComponent(value)}`)
      } else if (key === 'status') {
        acc.push(`status=${encodeURIComponent(value)}`)
      } else if (key === 'test') {
        acc.push(`test=${encodeURIComponent(value)}`)
      }
    }
    return acc
  }, []).join('&')

  // Fetch historical results with filters (always fetch, even without explicit filters)
  const { data: historicalData, mutate: mutateHistory } = useSWR(
    () => `/api/results?page=${currentPage}&limit=${resultsPerPage}${queryString ? `&${queryString}` : ''}&sort_by=created_at&sort_order=desc`,
    apiGet
  )

  // SSE for live updates
  useEffect(() => {
    try {
      const es = new EventSource('/api/events')
      esRef.current = es
      es.onmessage = (ev) => {
        try {
          const data = JSON.parse(ev.data || '{}')
          if (data?.type === 'result_created' && data?.result) {
            setLiveResults(prev => [data.result, ...prev].slice(0, 100))
            // Refresh historical data on new result
            mutateHistory()
          }
        } catch {}
      }
      es.onerror = () => { /* allow auto-retry */ }
      return () => { try { esRef.current?.close() } catch {} }
    } catch {}
  }, [queryString, mutateHistory])

  // Filter live results locally
  useEffect(() => {
    let filtered = [...liveResults, ...(historicalData || [])]

    if (filters.asset) filtered = filtered.filter(r => r.asset_number?.toLowerCase().includes(filters.asset.toLowerCase()))
    if (filters.mac) filtered = filtered.filter(r => r.mac?.toLowerCase().includes(filters.mac.toLowerCase()))
    if (filters.status) filtered = filtered.filter(r => r.status === filters.status)
    if (filters.test) filtered = filtered.filter(r => r.test_name?.toLowerCase().includes(filters.test.toLowerCase()))

    setFilteredResults(filtered.slice(0, 200)) // Show up to 200 results
  }, [liveResults, historicalData, filters])

  const hasFilters = Object.values(filters).some(val => val.trim())
  const displayResults = filteredResults

  const formatDate = (dateStr) => {
    if (!dateStr) return '-'
    const date = new Date(dateStr)
    if (isNaN(date)) return '-'

    try {
      return date.toLocaleString('en-US', {
        timeZone: 'America/Chicago',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (e) {
      return date.toLocaleString()
    }
  }

  return (
    <Layout>
      <div className="grid">
        <Card title="Live Activity">
          <div style={{ marginBottom: '16px' }}>
            <button
              className="btn-secondary"
              onClick={() => setShowFilters(!showFilters)}
              style={{ marginRight: '8px' }}
            >
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </button>
            <button
              className="btn-secondary"
              onClick={() => mutateHistory()}
              style={{ marginRight: '8px' }}
            >
              Refresh
            </button>
          </div>

          {showFilters && (
            <div style={{ marginBottom: '16px', padding: '12px', background: '#0f1521', borderRadius: '8px' }}>
              <div className="grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px' }}>
                <label>
                  <span>Asset Number</span>
                  <input
                    value={filters.asset}
                    onChange={(e) => setFilters(prev => ({ ...prev, asset: e.target.value }))}
                    placeholder="e.g. 123456"
                  />
                </label>
                <label>
                  <span>MAC Address</span>
                  <input
                    value={filters.mac}
                    onChange={(e) => setFilters(prev => ({ ...prev, mac: e.target.value }))}
                    placeholder="e.g. 00:11:22:33:44:55"
                  />
                </label>
                <label>
                  <span>Test Name</span>
                  <input
                    value={filters.test}
                    onChange={(e) => setFilters(prev => ({ ...prev, test: e.target.value }))}
                    placeholder="e.g. ping"
                  />
                </label>
                <label>
                  <span>Status</span>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  >
                    <option value="">All</option>
                    <option value="passed">Passed</option>
                    <option value="failed">Failed</option>
                    <option value="skipped">Skipped</option>
                    <option value="running">Running</option>
                  </select>
                </label>
                <label>
                  <span>From Date</span>
                  <input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  />
                </label>
                <label>
                  <span>To Date</span>
                  <input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                  />
                </label>
              </div>
              <div style={{ marginTop: '12px' }}>
                <button
                  className="btn-secondary"
                  onClick={() => setFilters({ asset: '', mac: '', status: '', test: '', dateFrom: getTodayDate(), dateTo: getTomorrowDate() })}
                >
                  Reset to Today
                </button>
              </div>
            </div>
          )}

          <p style={{margin:'0 0 12px', color:'var(--muted)'}}>
            Streaming newest results first. Leave this page open to monitor activity.
            {hasFilters && ' (Showing filtered results)'}
          </p>

          {displayResults.length === 0 ? (
            <p>No results yet. Post to <code>/api/results</code> to see live updates.</p>
          ) : (
            <div style={{overflowX:'auto'}}>
              <table className="table">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Asset</th>
                    <th>MAC</th>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Profile</th>
                  </tr>
                </thead>
                <tbody>
                  {displayResults.map((r, index) => (
                    <tr key={`${r.id || index}`}>
                      <td title={r.created_at ? new Date(r.created_at).toISOString() : '-'}>
                        {formatDate(r.created_at)}
                      </td>
                      <td>{r.asset_number || '-'}</td>
                      <td>{r.mac || '-'}</td>
                      <td>{r.test_name}</td>
                      <td>
                        <span className={`pill ${r.status}`}>
                          {r.status}
                        </span>
                      </td>
                      <td>{r.profile_name || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>

        <Card title="Activity Statistics">
          {stats ? (
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-value">{stats.total_count}</div>
                <div className="stat-label">Total Results</div>
              </div>
              <div className="stat-item">
                <div className="stat-value">{stats.unique_assets}</div>
                <div className="stat-label">Assets Tested</div>
              </div>
              <div className="stat-item">
                <div className="stat-value">{stats.recent_activity}</div>
                <div className="stat-label">Results (24h)</div>
              </div>
              <div className="stat-item">
                <div className="stat-breakdown">
                  {Object.entries(stats.status_breakdown || {}).map(([status, count]) => (
                    <div key={status} className="stat-status">
                      <span className={`pill ${status} stat-pill`}>{status}:</span>
                      <strong>{count}</strong>
                    </div>
                  ))}
                </div>
                <div className="stat-label">Status Breakdown</div>
              </div>
            </div>
          ) : (
            <p>Loading statistics...</p>
          )}
        </Card>
      </div>

      <style jsx>{`
        .grid { display: grid; grid-template-columns: 1fr; gap: 24px; max-width: 1400px; margin: 0 auto; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 8px; border-bottom: 1px solid var(--border); text-align: left; }
        .pill { padding: 2px 8px; border-radius: 10px; font-size: 12px; color: #fff; text-transform: uppercase; font-weight: 700; }
        .pill.passed { background: #16a34a; }
        .pill.failed { background: #dc2626; }
        .pill.skipped { background: #6b7280; }
        .pill.running { background: #2563eb; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; }
        .stat-item { padding: 16px; background: #0f1521; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 32px; font-weight: bold; color: var(--text); margin-bottom: 4px; }
        .stat-label { color: var(--muted); font-size: 14px; }
        .stat-breakdown { margin-top: 8px; display: flex; flex-direction: column; gap: 4px; }
        .stat-status { display: flex; align-items: center; justify-content: center; gap: 8px; }
        .stat-pill { font-size: 11px; padding: 2px 6px; margin-bottom: 0; }
        @media (min-width: 1024px) {
          .grid { grid-template-columns: 1fr 300px; }
        }
      `}</style>
    </Layout>
  )
}
