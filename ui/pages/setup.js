import { useState } from 'react'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'
import { apiGet, apiPost } from '../src/lib/api'

export default function Setup() {
  const [step, setStep] = useState(1)
  const [busy, setBusy] = useState(false)
  const [error, setError] = useState('')

  // Website settings
  const [baseUrl, setBaseUrl] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [validated, setValidated] = useState(false)

  // PXE settings
  const [kernelUrl, setKernelUrl] = useState('/artifacts/vmlinuz')
  const [initrdUrl, setInitrdUrl] = useState('/artifacts/initrd.img')
  const [kernelArgs, setKernelArgs] = useState('boot=live fetch=http://nexus.lan/artifacts/rootfs.squashfs')

  async function saveWebsite() {
    setBusy(true); setError(''); setValidated(false)
    try {
      if (!baseUrl || !apiKey) {
        setError('Please enter Website URL and API Key.')
        return
      }
      await apiPost('/api/settings/website', { base_url: baseUrl, api_key: apiKey })
      // Validate API key by checking credits balance
      const res = await apiGet('/api/credits/balance')
      if (res && res.configured && !res.error) {
        setValidated(true)
        setStep(2)
      } else {
        setError(res?.error || 'Could not validate API connection. Please check URL and key.')
      }
    } catch (e) {
      setError(String(e.message || e))
    } finally {
      setBusy(false)
    }
  }

  async function savePXE(skip = false) {
    setBusy(true); setError('')
    try {
      if (!skip) {
        await apiPost('/api/settings/pxe', {
          kernel_url: kernelUrl || '',
          initrd_url: initrdUrl || '',
          kernel_args: kernelArgs || ''
        })
      }
      setStep(3)
    } catch (e) {
      setError(String(e.message || e))
    } finally {
      setBusy(false)
    }
  }

  function finish() {
    window.location.href = '/'
  }

  return (
    <Layout>
      <div className="wrap">
        <h1>Welcome to Nexus</h1>
        <p className="lead">Let’s complete a few quick steps to get you running.</p>

        <div className="steps">
          <div className={`step ${step===1?'active':''}`}>1. Website</div>
          <div className={`step ${step===2?'active':''}`}>2. PXE</div>
          <div className={`step ${step===3?'active':''}`}>3. Finish</div>
        </div>

        {step === 1 && (
          <Card title="Connect to Nexus Website">
            <div className="form">
              <label>
                <span>Website URL</span>
                <input placeholder="https://nexus.example.com" value={baseUrl} onChange={e => setBaseUrl(e.target.value)} />
              </label>
              <label>
                <span>API Key</span>
                <input placeholder="sk_..." value={apiKey} onChange={e => setApiKey(e.target.value)} />
              </label>
              {error && <div className="error">{error}</div>}
              {validated && <div className="ok">Website connection validated</div>}
              <div className="actions">
                <button className="btn-primary" disabled={busy} onClick={saveWebsite}>{busy ? 'Saving…' : 'Save & Continue'}</button>
              </div>
            </div>
          </Card>
        )}

        {step === 2 && (
          <Card title="PXE Defaults">
            <div className="form">
              <label>
                <span>Kernel URL</span>
                <input value={kernelUrl} onChange={e => setKernelUrl(e.target.value)} />
              </label>
              <label>
                <span>Initrd URL</span>
                <input value={initrdUrl} onChange={e => setInitrdUrl(e.target.value)} />
              </label>
              <label>
                <span>Kernel Args</span>
                <input value={kernelArgs} onChange={e => setKernelArgs(e.target.value)} />
              </label>
              {error && <div className="error">{error}</div>}
              <div className="actions">
                <button className="btn-secondary" disabled={busy} onClick={() => savePXE(true)}>Skip for now</button>
                <button className="btn-primary" disabled={busy} onClick={() => savePXE(false)}>{busy ? 'Saving…' : 'Save & Continue'}</button>
              </div>
            </div>
          </Card>
        )}

        {step === 3 && (
          <Card title="All set!">
            <p>Your Nexus Hub is configured. You can change these anytime in <a href="/settings">Settings</a>.</p>
            <div className="actions">
              <button className="btn-primary" onClick={finish}>Go to Dashboard</button>
            </div>
          </Card>
        )}

        <p className="help">Need help? Visit <a href="/help">Help</a>.</p>
      </div>

      <style jsx>{`
        .wrap { max-width: 900px; margin: 30px auto; padding: 0 16px; }
        .lead { color: var(--muted); margin-top: 6px; }
        .steps { display: flex; gap: 10px; margin: 14px 0 18px; }
        .step { padding: 6px 10px; border:1px solid var(--border); border-radius: 999px; color: var(--muted); }
        .step.active { background:#0f1521; color: var(--text); border-color:#24304d; }
        .form { display: grid; gap: 14px; }
        label { display: grid; gap: 6px; }
        input { background:#0b101b; color: var(--text); border:1px solid #1f2840; border-radius:8px; padding:10px; }
        .actions { display:flex; gap:10px; margin-top: 6px; }
        .btn-primary { background:#1c64f2; color:white; border:1px solid #184fcc; border-radius:8px; padding:8px 14px; }
        .btn-primary:hover { background:#1757d6; }
        .btn-secondary { background:#0b101b; color:var(--text); border:1px solid #1f2840; border-radius:8px; padding:8px 14px; }
        .error { color:#ff6868; background:#2a1212; border:1px solid #5a1e1e; padding:8px 10px; border-radius:8px; }
        .ok { color:#4ade80; background:#0f2a1f; border:1px solid #1f4d3a; padding:8px 10px; border-radius:8px; }
        .help { color: var(--muted); margin-top: 16px; }
      `}</style>
    </Layout>
  )
}
