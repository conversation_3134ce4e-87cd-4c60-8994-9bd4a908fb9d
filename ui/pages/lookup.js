import { useState } from 'react'
import useSWR from 'swr'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'
import { apiGet, apiDelete } from '../src/lib/api'

export default function AssetLookup() {
  const [asset, setAsset] = useState('')
  const [mac, setMac] = useState('')
  const [queryKey, setQueryKey] = useState(null)
  const [advancedFilters, setAdvancedFilters] = useState({
    status: '',
    test: '',
    profile: '',
    operator: '',
    dateFrom: '',
    dateTo: ''
  })
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [currentTab, setCurrentTab] = useState('results')

  const buildQuery = () => {
    if (!asset && !mac && !Object.values(advancedFilters).some(v => v)) return null

    const params = new URLSearchParams()
    if (asset) params.set('asset', asset)
    if (mac) params.set('mac', mac)
    if (advancedFilters.status) params.set('status', advancedFilters.status)
    if (advancedFilters.test) params.set('test', advancedFilters.test)
    if (advancedFilters.profile) params.set('profile', advancedFilters.profile)
    if (advancedFilters.operator) params.set('operator', advancedFilters.operator)
    if (advancedFilters.dateFrom) params.set('start_date', new Date(advancedFilters.dateFrom).toISOString().split('T')[0])
    if (advancedFilters.dateTo) params.set('end_date', new Date(advancedFilters.dateTo).toISOString().split('T')[0])
    return params.toString()
  }

  const query = buildQuery()

  const { data: latestResults } = useSWR(() => query ? `/api/results/latest?${query}` : null, apiGet)
  const { data: wipes } = useSWR(() => query ? `/api/wipes?${query}` : null, apiGet)
  const { data: allResults } = useSWR(() => query ? `/api/results?${query}&limit=1000` : null, apiGet)

  const exportData = (type) => {
    let data = []
    let filename = `lookup-export-${new Date().toISOString().split('T')[0]}`

    if (type === 'results' && allResults) {
      data = allResults.map(r => ({
        timestamp: r.created_at ? new Date(r.created_at).toLocaleString('en-US', { timeZone: 'America/Chicago' }) : '',
        asset_number: r.asset_number || '',
        mac: r.mac || '',
        test_name: r.test_name || '',
        status: r.status || '',
        operator: r.operator || '',  // ← ADD THIS
        profile_name: r.profile_name || '',
        run_id: r.run_id || '',
        category: r.category || ''
      }))
      filename += '-results.csv'
    } else if (type === 'wipes' && wipes) {
      data = wipes.map(w => ({
        timestamp: w.created_at ? new Date(w.created_at).toLocaleString('en-US', { timeZone: 'America/Chicago' }) : '',
        asset_number: w.asset_number || '',
        mac: w.mac || '',
        drive_serial: w.drive_serial || '',
        drive_model: w.drive_model || '',
        status: w.status || '',
        method: w.method_standard || '',
        started_at: w.started_at ? new Date(w.started_at).toLocaleString('en-US', { timeZone: 'America/Chicago' }) : '',
        ended_at: w.ended_at ? new Date(w.ended_at).toLocaleString('en-US', { timeZone: 'America/Chicago' }) : '',
        certificate_id: w.certificate_id || ''
      }))
      filename += '-wipes.csv'
    }

    if (data.length === 0) return

    const csvContent = 'data:text/csv;charset=utf-8,' +
      Object.keys(data[0]).join(',') + '\n' +
      data.map(row => Object.values(row).map(val =>
        typeof val === 'string' && val.includes(',') ? `"${val}"` : String(val || '')
      ).join(',')).join('\n')

    const encodedUri = encodeURI(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  function onSearch(e) {
    e.preventDefault()
    const searchParams = {
      asset: asset || undefined,
      mac: mac || undefined,
      ...Object.fromEntries(
        Object.entries(advancedFilters).filter(([_, value]) => value)
      )
    }
    setQueryKey(searchParams)
  }

  function clearFilters() {
    setAsset('')
    setMac('')
    setAdvancedFilters({ status: '', test: '', profile: '', operator: '', dateFrom: '', dateTo: '' })
    setQueryKey(null)
  }

  async function deleteItem(type, id, name) {
    if (!confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.\n\n${name}`)) {
      return
    }

    try {
      const endpoint = type === 'result' ? `/api/results/${id}` : `/api/wipes/${id}`
      await apiDelete(endpoint)

      // Refresh the data
      if (type === 'result') {
        mutateResults()
      } else {
        mutateWipes()
      }

      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully.`)
    } catch (error) {
      alert(`Failed to delete ${type}: ${error.message}`)
    }
  }

  function formatDate(s) {
    if (!s) return ''
    const d = new Date(s)
    if (isNaN(d)) return s

    // Use Central Time (Chicago) as default until timezone settings are available
    try {
      return d.toLocaleString('en-US', {
        timeZone: 'America/Chicago',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (e) {
      return d.toLocaleString()
    }
  }

  return (
    <Layout>
      <div className="grid">
        <Card title="Asset Lookup">
          <form onSubmit={onSearch} className="form">
            <div className="search-row">
              <label>
                <span>Asset Number</span>
                <input value={asset} onChange={e => setAsset(e.target.value)} placeholder="e.g. 123456" />
              </label>
              <label>
                <span>MAC Address</span>
                <input value={mac} onChange={e => setMac(e.target.value)} placeholder="e.g. 00:11:22:33:44:55" />
              </label>
              <div className="action-group">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  {showAdvanced ? 'Hide' : 'Show'} Advanced
                </button>
                <button className="btn-secondary" type="submit">Search</button>
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={clearFilters}
                >
                  Clear
                </button>
              </div>
            </div>

            {showAdvanced && (
              <div className="advanced-filters" style={{ marginTop: '16px', padding: '12px', background: '#0f1521', borderRadius: '8px' }}>
                <div className="grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' }}>
                  <label>
                    <span>Status</span>
                    <select
                      value={advancedFilters.status}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, status: e.target.value }))}
                    >
                      <option value="">All</option>
                      <option value="passed">Passed</option>
                      <option value="failed">Failed</option>
                      <option value="skipped">Skipped</option>
                      <option value="running">Running</option>
                    </select>
                  </label>
                  <label>
                    <span>Test Name</span>
                    <input
                      value={advancedFilters.test}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, test: e.target.value }))}
                      placeholder="e.g. ping"
                    />
                  </label>
                  <label>
                    <span>Profile</span>
                    <input
                      value={advancedFilters.profile}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, profile: e.target.value }))}
                      placeholder="e.g. network"
                    />
                  </label>
                  <label>
                    <span>Operator</span>
                    <input
                      value={advancedFilters.operator}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, operator: e.target.value }))}
                      placeholder="e.g. john.doe"
                    />
                  </label>
                  <label>
                    <span>From Date</span>
                    <input
                      type="date"
                      value={advancedFilters.dateFrom}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                    />
                  </label>
                  <label>
                    <span>To Date</span>
                    <input
                      type="date"
                      value={advancedFilters.dateTo}
                      onChange={(e) => setAdvancedFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                    />
                  </label>
                </div>
              </div>
            )}
          </form>
        </Card>

        <Card title="Search Results">
          {query ? (
            <>
              {/* Tab navigation */}
              <div className="tabs" style={{ marginBottom: '16px', borderBottom: '1px solid var(--border)' }}>
                <button
                  className={`tab-button ${currentTab === 'results' ? 'active' : ''}`}
                  onClick={() => setCurrentTab('results')}
                >
                  Test Results ({latestResults?.length || 0})
                </button>
                <button
                  className={`tab-button ${currentTab === 'wipes' ? 'active' : ''}`}
                  onClick={() => setCurrentTab('wipes')}
                >
                  Wipe Reports ({wipes?.length || 0})
                </button>
              </div>

              {/* Export buttons */}
              <div style={{ marginBottom: '16px' }}>
                {currentTab === 'results' && allResults?.length > 0 && (
                  <button
                    className="btn-secondary"
                    onClick={() => exportData('results')}
                  >
                    Export Results CSV
                  </button>
                )}
                {currentTab === 'wipes' && wipes?.length > 0 && (
                  <button
                    className="btn-secondary"
                    onClick={() => exportData('wipes')}
                  >
                    Export Wipes CSV
                  </button>
                )}
              </div>

              {/* Results content */}
              {currentTab === 'results' && (
                <>
                  {!latestResults && <div className="muted">Searching...</div>}
                  {latestResults && latestResults.length === 0 && <div className="muted">No results found.</div>}
                  {latestResults && latestResults.length > 0 && (
                    <div style={{overflowX:'auto'}}>
                      <table className="table">
                        <thead>
                          <tr>
                            <th>Asset Number</th>
                            <th>Operator</th>
                            <th>Test</th>
                            <th>Status</th>
                            <th>Profile</th>
                            <th>Run</th>
                            <th>Started</th>
                            <th>Ended</th>
                          </tr>
                        </thead>
                        <tbody>
                          {latestResults.map(r => (
                            <tr key={r.id}>
                              <td><strong>{r.asset_number || '-'}</strong></td>
                              <td>{r.operator || '-'}</td>
                              <td>{r.test_name}</td>
                              <td><span className={`pill ${r.status}`}>{r.status}</span></td>
                              <td>{r.profile_name || '-'}</td>
                              <td>{r.run_id || ''}</td>
                              <td>{formatDate(r.started_at)}</td>
                              <td>{formatDate(r.ended_at)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </>
              )}

              {/* Wipes content */}
              {currentTab === 'wipes' && (
                <>
                  {!wipes && <div className="muted">Searching...</div>}
                  {wipes && wipes.length === 0 && <div className="muted">No wipes found.</div>}
                  {wipes && wipes.length > 0 && (
                    <div style={{overflowX:'auto'}}>
                      <table className="table">
                        <thead>
                          <tr>
                            <th>Asset Number</th>
                            <th>Status</th>
                            <th>Drive Serial</th>
                            <th>Model</th>
                            <th>Method</th>
                            <th>Started</th>
                            <th>Ended</th>
                            <th>Certificate</th>
                          </tr>
                        </thead>
                        <tbody>
                          {wipes.map(w => (
                            <tr key={w.id}>
                              <td><strong>{w.asset_number || '-'}</strong></td>
                              <td><span className={`pill ${w.status}`}>{w.status}</span></td>
                              <td>{w.drive_serial || ''}</td>
                              <td>{w.drive_model || ''}</td>
                              <td>{w.method_standard || w.method_technique || ''}</td>
                              <td>{formatDate(w.started_at)}</td>
                              <td>{formatDate(w.ended_at)}</td>
                              <td>
                                <a className="btn-link" href={`/wipes/${w.id}/certificate.html`} target="_blank" rel="noreferrer">View</a>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </>
              )}
            </>
          ) : (
            <div className="muted">Enter an asset number or MAC address and click Search to find related records.</div>
          )}
        </Card>
      </div>
      <style jsx>{`
        .muted { color: var(--muted); }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 8px; border-bottom: 1px solid var(--border); text-align: left; }
        .pill { padding: 2px 8px; border-radius: 10px; font-size: 12px; color: #fff; text-transform: uppercase; font-weight: 700; }
        .pill.passed { background: #16a34a; }
        .pill.failed { background: #dc2626; }
        .pill.skipped { background: #6b7280; }
        .pill.running { background: #2563eb; }
        .btn-link { color: #60a5fa; text-decoration: none; }
        .btn-link:hover { text-decoration: underline; }
        .search-row { display: flex; gap: 12px; align-items: end; flex-wrap: wrap; }
        .search-row label { flex: 1; min-width: 150px; }
        .action-group { display: flex; gap: 8px; }
        .tabs { display: flex; }
        .tab-button {
          padding: 8px 16px;
          background: none;
          border: none;
          color: var(--muted);
          cursor: pointer;
          border-bottom: 3px solid transparent;
          font-weight: 500;
        }
        .tab-button.active {
          color: var(--text);
          border-bottom-color: #60a5fa;
        }
        .tab-button:hover { color: var(--text); }
        .grid { display: grid; grid-template-columns: 1fr; gap: 24px; }
      `}</style>
    </Layout>
  )
}
