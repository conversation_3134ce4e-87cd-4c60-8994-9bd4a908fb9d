import { useRouter } from 'next/router'
import Layout from '../../src/components/Layout'
import Card from '../../src/components/Card'

export default function WipeCertificatePage() {
  const router = useRouter()
  const { id } = router.query
  const API_BASE = (process.env.NEXT_PUBLIC_API_BASE || '').replace(/\/$/, '')
  const buildApiUrl = (path) => {
    let p = path || ''
    if (p && !p.startsWith('/')) p = `/${p}`
    if (API_BASE.endsWith('/api') && p.startsWith('/api/')) {
      p = p.replace(/^\/api/, '')
    }
    return `${API_BASE}${p}` || p
  }
  const iframeSrc = id ? buildApiUrl(`/api/wipes/${id}/certificate.html`) : ''

  return (
    <Layout>
      <Card title={`Wipe Certificate${id ? ` #${id}` : ''}`}>
        {!id ? (
          <div className="muted">Loading…</div>
        ) : (
          <>
            <div className="actions">
              <a className="btn-secondary" href={iframeSrc} target="_blank" rel="noreferrer">Open in new tab</a>
              <button className="btn-secondary" onClick={() => window.print()}>Print</button>
            </div>
            <div className="frameWrap">
              {/* Using an iframe so the server-rendered cert HTML and styles are preserved */}
              <iframe title="Wipe Certificate" src={iframeSrc} />
            </div>
          </>
        )}
      </Card>
      <style jsx>{`
        .muted { color: var(--muted); }
        .actions { display:flex; gap:8px; margin: 0 0 8px; }
        .frameWrap { border:1px solid var(--border); border-radius:8px; overflow:hidden; background:#0b101b; }
        iframe { width:100%; height: 80vh; border:0; background:#fff; }
        @media print {
          header, nav, footer, .actions { display: none !important; }
          .frameWrap, iframe { height: auto; }
        }
      `}</style>
    </Layout>
  )
}
