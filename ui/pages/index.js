import useSWR from 'swr'
import { useEffect, useState } from 'react'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'
import { apiGet, apiPost } from '../src/lib/api'

const fetcher = (url) => apiGet(url)

export default function Home() {
  const { data: pxe, mutate: mutatePxe } = useSWR('/api/settings/pxe', fetcher)

  const [form, setForm] = useState({ kernel_url: '', initrd_url: '', kernel_args: '' })
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  
  // no-op

  useEffect(() => {
    if (pxe) setForm(pxe)
  }, [pxe])

  // (Live Results moved to /live)

  async function onSave(e) {
    e.preventDefault()
    setSaving(true)
    setSaved(false)
    try {
      const res = await apiPost('/api/settings/pxe', form)
      await mutatePxe(res, false)
      setSaved(true)
    } finally {
      setSaving(false)
      setTimeout(() => setSaved(false), 2000)
    }
  }

  return (
    <Layout>
      <div className="grid">
        <Card title="Quick Actions">
          <div className="quick-actions">
            <a className="qa" href="/profiles">
              <div className="qa-title">Manage Profiles</div>
              <div className="qa-sub">Create/edit test bundles</div>
            </a>
            <a className="qa" href="/devices">
              <div className="qa-title">Devices</div>
              <div className="qa-sub">Per‑MAC overrides</div>
            </a>
            <a className="qa" href="/live">
              <div className="qa-title">Live Results</div>
              <div className="qa-sub">Latest test activity</div>
            </a>
            <a className="qa" href="/settings">
              <div className="qa-title">Settings</div>
              <div className="qa-sub">Website/API connection & keys</div>
            </a>
            <a className="qa" href="/artifacts/" target="_blank" rel="noreferrer">
              <div className="qa-title">Artifacts</div>
              <div className="qa-sub">Kernel, initrd, rootfs files</div>
            </a>
          </div>
        </Card>

        <Card title="PXE Boot Settings">
          <form onSubmit={onSave} className="form">
            <label>
              <span>Kernel URL</span>
              <input value={form.kernel_url || ''} onChange={e => setForm({ ...form, kernel_url: e.target.value })} placeholder="/artifacts/vmlinuz or http://nexus.lan/artifacts/vmlinuz" />
              <small className="hint">Path or full URL to the Linux kernel image</small>
            </label>
            <label>
              <span>Initrd URL</span>
              <input value={form.initrd_url || ''} onChange={e => setForm({ ...form, initrd_url: e.target.value })} placeholder="/artifacts/initrd.img" />
              <small className="hint">Initramfs image used during boot</small>
            </label>
            <label>
              <span>Kernel Args</span>
              <input value={form.kernel_args || ''} onChange={e => setForm({ ...form, kernel_args: e.target.value })} placeholder="boot=live fetch=http://nexus.lan/artifacts/rootfs.squashfs" />
              <small className="hint">Additional boot parameters (e.g. fetch=rootfs URL)</small>
            </label>
            <div className="actions">
              <button className="btn-secondary" type="submit" disabled={saving}>{saving ? 'Saving...' : 'Save'}</button>
              {saved && <span className="ok" style={{ marginLeft: 8 }}>Saved</span>}
            </div>
          </form>
        </Card>

                
      </div>
      <style jsx>{`
        .quick-actions { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
        .qa { display: flex; flex-direction: column; gap: 4px; border: 1px solid var(--border); background:#0f1521; border-radius:10px; padding:12px; text-decoration:none; color: var(--text); }
        .qa:hover { background:#0b101b; border-color:#2a3350; }
        .qa-title { font-weight: 700; }
        .qa-sub { color: var(--muted); font-size: 12px; }
        .hint { display:block; color: var(--muted); margin-top: 4px; }
        .grid { min-height: 100vh; }
        @media (max-width: 900px) { .quick-actions { grid-template-columns: 1fr; } }
      `}</style>
    </Layout>
  )
}
