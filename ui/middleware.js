import { NextResponse } from 'next/server'

// Paths that should never be gated
const EXEMPT = [
  '/setup',
  '/api',
  '/_next',
  '/artifacts',
  '/favicon.ico',
]

export async function middleware(req) {
  const { pathname } = req.nextUrl

  // Exempted paths
  if (EXEMPT.some(p => pathname === p || pathname.startsWith(p + '/'))) {
    return NextResponse.next()
  }

  // Ask API if setup is complete
  try {
    const url = new URL('/api/setup/status', req.url)
    const res = await fetch(url.toString(), { headers: { 'Accept': 'application/json' } })
    if (res.ok) {
      const status = await res.json()
      if (!status?.configured) {
        const setupUrl = new URL('/setup', req.url)
        return NextResponse.redirect(setupUrl)
      }
    }
  } catch (e) {
    // If API is unavailable, allow navigation instead of blocking
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // Gate everything except assets and exempt list above; middleware will re-check
    '/((?!_next|api|artifacts|favicon.ico).*)',
  ],
}
