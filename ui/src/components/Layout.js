import useSWR from 'swr'
import { apiGet } from '../lib/api'
import { useEffect, useMemo, useState } from 'react'

const fetcher = (url) => apiGet(url)

export default function Layout({ children }) {
  const { data: health } = useSWR('/api/health', fetcher, { refreshInterval: 5000 })
  const status = health?.status === 'ok' ? 'ok' : (health ? 'warn' : 'idle')
  const [open, setOpen] = useState(false)
  const adminBase = typeof window !== 'undefined' ? `http://${window.location.hostname}:8000` : ''
  const { data: websiteSettings } = useSWR('/api/settings/website', fetcher)
  // Credits balance (refresh every 60 minutes)
  const { data: credits } = useSWR('/api/credits/balance', fetcher, {
    refreshInterval: 60 * 60 * 1000,
    revalidateOnFocus: false,
  })
  const onSettingsPage = typeof window !== 'undefined' ? window.location.pathname.startsWith('/settings') : false
  const needsOnboarding = useMemo(() => {
    if (!websiteSettings) return false
    const missingUrl = !websiteSettings.base_url
    const missingKey = !websiteSettings.api_key_set
    return (missingUrl || missingKey) && !onSettingsPage
  }, [websiteSettings, onSettingsPage])

  return (
    <div className="app">
      <header className="app-header">
        <div className="lh">
          <h1>Nexus</h1>
          <div className="sub">PXE Hub (Relay mode)</div>
        </div>
        <div className="rh" title={health ? `API: ${health.status}` : 'API: checking...'}>
          <span className={`dot ${status}`} />
          <span className="label">API</span>
          <div className="divider" />
          <span className="label" title="Remaining credits">Credits</span>
          <span>{
            credits
              ? (credits.configured && !credits.error && typeof credits.balance === 'number'
                  ? credits.balance
                  : '—')
              : '…'
          }</span>
          <div className="divider" />
          <a className="btn-secondary link-btn" href="/settings">Settings</a>
          <div className="menu">
            <button className="btn-secondary" onClick={() => setOpen(v => !v)}>Actions</button>
            {open && (
              <div className="menu-pop">
                <div className="menu-title">Docker actions</div>
                <button className="menu-item" onClick={async () => {
                  try {
                    // Start UI (safe, doesn't restart API)
                    const r = await fetch(`${adminBase}/api/admin/docker/start`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'ui' }) })
                    if (!r.ok) throw new Error('request failed')
                  } catch (e) {
                    try { await navigator.clipboard.writeText('docker compose -f Nexus/infra/docker-compose.yml up -d') } catch {}
                  } finally { setOpen(false) }
                }}>Start Nexus</button>
                <button className="menu-item" onClick={async () => {
                  try {
                    // Restart UI + Nginx only (avoid API self-restart 502)
                    const r1 = await fetch(`${adminBase}/api/admin/docker/restart`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'ui' }) })
                    if (!r1.ok) throw new Error('request failed')
                    const r2 = await fetch(`${adminBase}/api/admin/docker/restart`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'nginx' }) })
                    if (!r2.ok) throw new Error('request failed')
                  } catch {
                    try { await navigator.clipboard.writeText('docker compose -f Nexus/infra/docker-compose.yml restart nexus-ui nexus-nginx') } catch {}
                  } finally { setOpen(false) }
                }}>Restart Nexus</button>
                <button className="menu-item" onClick={async () => {
                  try {
                    // Recreate UI + Nginx
                    const r1 = await fetch(`${adminBase}/api/admin/docker/stop`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'ui' }) })
                    if (!r1.ok) throw new Error('request failed')
                    const r1b = await fetch(`${adminBase}/api/admin/docker/stop`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'nginx' }) })
                    if (!r1b.ok) throw new Error('request failed')
                    const r2 = await fetch(`${adminBase}/api/admin/docker/start`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'ui' }) })
                    if (!r2.ok) throw new Error('request failed')
                    const r2b = await fetch(`${adminBase}/api/admin/docker/start`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ service: 'nginx' }) })
                    if (!r2b.ok) throw new Error('request failed')
                  } catch {
                    try { await navigator.clipboard.writeText('docker compose -f Nexus/infra/docker-compose.yml down nexus-ui nexus-nginx && docker compose -f Nexus/infra/docker-compose.yml up -d nexus-ui nexus-nginx') } catch {}
                  } finally { setOpen(false) }
                }}>Recreate (down/up)</button>
              </div>
            )}
          </div>
        </div>
      </header>
      {needsOnboarding && (
        <div className="banner">
          <div className="banner-text">
            <strong>Finish setup:</strong> Configure your Nexus Website URL and API key to enable credits.
          </div>
          <a className="btn-secondary" href="/settings">Open Settings</a>
        </div>
      )}
      <nav className="topnav">
        <a href="/">Home</a>
        <a href="/profiles">Profiles</a>
        <a href="/devices">Devices</a>
        <a href="/live">Activity</a>
        <a href="/lookup">Lookup</a>
        <a href="/settings">Settings</a>
      </nav>
      <main className="app-main">{children}</main>
      <footer className="app-footer">
        <div className="spacer" />
        <div className="links">
          <a href="/help">Docs</a>
          <a
            href={websiteSettings?.base_url ? `${websiteSettings.base_url}/support` : 'mailto:<EMAIL>?subject=Nexus%20Support'}
            target={websiteSettings?.base_url ? '_blank' : undefined}
            rel={websiteSettings?.base_url ? 'noreferrer' : undefined}
          >Support</a>
        </div>
      </footer>
      <style jsx>{`
        .app { min-height: 100vh; position: relative; }
        .app::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image: url(/blueblur.jpg);
          background-size: cover;
          background-position: center;
          background-attachment: fixed;
          opacity: 0.5;
          z-index: -1;
        }
        .app-header { display:flex; align-items:center; justify-content:space-between; }
        .lh { display:flex; flex-direction:column; }
        .rh { position:relative; display:flex; align-items:center; gap:8px; color: var(--muted); }
        .banner { display:flex; justify-content:space-between; align-items:center; gap:10px; background:#0b101b; border:1px solid var(--border); border-radius:8px; padding:10px 12px; margin: 10px 0; }
        .banner-text { color: var(--text); }
        .dot { width:10px; height:10px; border-radius:999px; display:inline-block; background:#6b7280; box-shadow: 0 0 0 1px rgba(255,255,255,0.05) inset; }
        .dot.ok { background:#39d353; }
        .dot.warn { background:#f59e0b; }
        .dot.idle { background:#6b7280; }
        .label { font-size:12px; text-transform:uppercase; letter-spacing:0.04em; }
        .divider { width:1px; height:14px; background: var(--border); margin: 0 6px; }
        .menu { position:relative; }
        .menu-pop { position:absolute; right:0; top:28px; background:#0f1521; border:1px solid var(--border); border-radius:8px; padding:8px; display:flex; flex-direction:column; gap:6px; min-width:240px; z-index:10; }
        .menu-title { font-size:12px; color: var(--muted); margin-bottom:2px; }
        .menu-item { text-align:left; background:#121722; color: var(--text); border:1px solid var(--border); border-radius:6px; padding:6px 8px; }
        .menu-item:hover { background:#0b101b; border-color:#2a3350; }
        .link-btn { text-decoration:none; display:inline-flex; align-items:center; }
        .topnav { display:flex; gap:12px; margin: 10px 0 16px; padding-bottom:8px; border-bottom: 1px solid var(--border); }
        .topnav a { color: var(--text); text-decoration:none; padding:6px 10px; border-radius:6px; border:1px solid transparent; }
        .topnav a:hover { background:#0b101b; border:1px solid var(--border); }
        .app-footer { display:flex; align-items:center; justify-content:space-between; padding: 8px 0 0; margin-top: 12px; border-top: 1px solid var(--border); color: var(--muted); }
        .app-footer .links { display:flex; gap:12px; }
        .app-footer a { color: var(--muted); text-decoration:none; }
        .app-footer a:hover { color: var(--text); }
        .app-footer a.disabled { opacity: 0.5; pointer-events: none; }
      `}</style>
    </div>
  )
}
