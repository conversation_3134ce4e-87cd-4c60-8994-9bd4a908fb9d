import Image from 'next/image';
import Button from '@/components/Button';

const features = [
  {
    name: "Nexus",
    title: "Orchestrate Your Entire ITAD Workflow",
    description: "Nexus provides a centralized command center for all your hardware management needs. From PXE booting to secure data wiping, gain complete control and visibility over your IT assets, ensuring a seamless and efficient operational flow.",
    image: "/nexusherobg.png",
    details: [
      "Centralized PXE/iPXE artifact hosting for streamlined network booting.",
      "Real-time device session monitoring and remote control.",
      "Comprehensive and secure wipe orchestration with detailed reporting.",
      "In-depth hardware diagnostics to quickly assess asset health.",
      "Immutable compliance audit trails for guaranteed accountability.",
      "A powerful RESTful API to automate and integrate your workflows."
    ]
  },
  {
    name: "Foundry",
    title: "Craft Your Perfect Boot Environment",
    description: "Foundry is your workshop for building highly customized, reproducible Linux images. Tailor-made for hardware testing and secure data erasure, it empowers you to create the precise tools you need for any task.",
    image: "/foundrybg.png",
    details: [
      "Debian Live-based image building for a stable and flexible foundation.",
      "A fully containerized build environment for consistency and portability.",
      "Custom kernel and initrd generation to meet specific hardware needs.",
      "PXE-ready artifact production for instant deployment on your network.",
      "Granular control over package selection for lean and efficient images.",
      "Produces both ISO and netboot formats for maximum flexibility."
    ]
  },
  {
    name: "Crucible",
    title: "Execute with Uncompromising Precision",
    description: "Crucible is the edge agent that gets the job done. Deployed on target devices, it performs exhaustive hardware diagnostics and executes NIST 800-88 compliant data destruction with flawless accuracy and reliability.",
    image: "/cruciblebg.png",
    details: [
      "NIST 800-88 compliant data wiping for certifiable security.",
      "Real-time progress reporting back to the Nexus server.",
      "Detailed hardware inventory collection for asset tracking.",
      "Cryptographically secure erase verification and certificate generation.",
      "Designed for reliability in diverse and challenging hardware environments."
    ]
  },
  {
    name: "Integration Suite",
    title: "Achieve End-to-End Workflow Automation",
    description: "The Integration Suite brings it all together, creating a seamless, automated workflow from image creation to final audit. It's the ultimate solution for enterprise-grade ITAD operations, delivering efficiency, compliance, and peace of mind.",
    image: "/nexus_square4.png",
    details: [
      "Seamless PXE network booting for hands-off device provisioning.",
      "Fully automated workflow orchestration from start to finish.",
      "Enterprise-grade reporting for management and compliance.",
      "Scalable multi-device batch operations for high-volume environments.",
      "Automated generation of compliance and auditing documentation.",
      "Flexible deployment options for both cloud and on-premise infrastructures."
    ]
  }
];

export default function Features() {
  return (
    <div className="bg-black text-white">
      {/* Hero Section */}
      <div className="relative h-[60vh] min-h-[400px] flex items-center justify-center text-center">
        <Image
          src="/nexus_wallpaper_landscape.png"
          alt="Nexus Nebula"
          layout="fill"
          objectFit="cover"
          className="absolute inset-0 z-0 opacity-30"
        />
        <div className="relative z-10 p-8">
          <h1 className="text-5xl md:text-7xl font-extrabold mb-4 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">
            The Future of Hardware Management
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-gray-300 mb-8">
            The Nexus Suite provides an unparalleled, end-to-end solution for ITAD professionals. Automate, manage, and secure your hardware lifecycle with confidence and precision.
          </p>
          <Button href="/pricing" size="lg" className="bg-cyan-500 hover:bg-cyan-600 text-white">
            Get Started
          </Button>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {features.map((feature, index) => (
            <div key={feature.name} className={`flex flex-col md:flex-row items-center gap-8 lg:gap-12 ${index % 2 === 1 ? 'md:flex-row-reverse' : ''} ${index > 0 ? 'mt-16 sm:mt-24' : ''}`}>
              <div className="md:w-1/2">
                <div className="relative w-full h-80 rounded-lg shadow-2xl overflow-hidden">
                  <Image
                    src={feature.image}
                    alt={feature.name}
                    layout="fill"
                    objectFit="cover"
                  />
                </div>
              </div>
              <div className="md:w-1/2">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-sky-500 bg-clip-text text-transparent mb-3">
                  {feature.name}
                </h2>
                <h3 className="text-2xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.details.map((detail, i) => (
                    <li key={i} className="flex items-start">
                      <svg className="w-5 h-5 mr-3 text-cyan-400 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path></svg>
                      <span className="text-gray-400">{detail}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}