from __future__ import annotations
import re
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from .config import settings


def _is_sqlite(url: str) -> bool:
    return url.startswith("sqlite")


connect_args = {"check_same_thread": False} if _is_sqlite(settings.DB_URL) else {}
engine = create_engine(settings.DB_URL, echo=False, future=True, connect_args=connect_args)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)
Base = declarative_base()


def init_db() -> None:
    # Import models so metadata is populated
    from .models import device, session as session_model, setting, profile  # noqa: F401
    from .models import result  # noqa: F401
    from .models import wipe  # noqa: F401
    from .models import device_override  # noqa: F401
    Base.metadata.create_all(bind=engine)
    # Lightweight migrations for new columns on results
    if _is_sqlite(settings.DB_URL):
        # SQLite migrations
        with engine.connect() as conn:
            # Check if columns exist
            res = conn.execute(text("PRAGMA table_info(results)")).fetchall()
            cols = {r[1] for r in res}
            if "run_id" not in cols:
                conn.execute(text("ALTER TABLE results ADD COLUMN run_id VARCHAR(64)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS ix_results_run_id ON results(run_id)"))
            if "category" not in cols:
                conn.execute(text("ALTER TABLE results ADD COLUMN category VARCHAR(64)"))
            if "operator" not in cols:
                conn.execute(text("ALTER TABLE results ADD COLUMN operator VARCHAR(128)"))
    else:
        # PostgreSQL migrations
        with engine.connect() as conn:
            # Check if operator column exists
            res = conn.execute(text("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'results' AND column_name = 'operator'
            """)).fetchall()
            if not res:
                # Add operator column to PostgreSQL
                conn.execute(text("ALTER TABLE results ADD COLUMN operator VARCHAR(128)"))
                conn.commit()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
