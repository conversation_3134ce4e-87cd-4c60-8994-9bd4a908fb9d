from __future__ import annotations
from datetime import datetime, timezone, timedelta
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, JSON

from ..db import Base


class Wipe(Base):
    __tablename__ = "wipes"

    id = Column(Integer, primary_key=True, index=True)
    asset_number = Column(String(128), index=True, nullable=True)
    mac = Column(String(32), index=True, nullable=True)
    run_id = Column(String(64), index=True, nullable=True)

    drive_model = Column(String(256), nullable=True)
    drive_serial = Column(String(256), index=True, nullable=True)
    drive_wwn = Column(String(128), nullable=True)
    drive_size_bytes = Column(Integer, nullable=True)
    interface = Column(String(32), nullable=True)

    method_standard = Column(String(128), nullable=True)
    method_technique = Column(String(128), nullable=True)
    passes = Column(Integer, nullable=True)

    verify_method = Column(String(64), nullable=True)
    verify_result = Column(String(32), nullable=True)
    verify_details = Column(JSON, nullable=True)

    status = Column(String(32), nullable=False, default="passed")
    operator = Column(String(128), nullable=True)
    location = Column(String(128), nullable=True)
    notes = Column(String(1024), nullable=True)
    logs = Column(JSON, nullable=True)

    started_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)

    certificate_id = Column(String(64), unique=True, index=True, nullable=False)

    # Use Central Time (UTC-5) for timestamps
    def _get_local_time():
        central_tz = timezone(timedelta(hours=-5))
        return datetime.now(central_tz).replace(tzinfo=None)
    
    created_at = Column(DateTime, default=_get_local_time, nullable=False)
    updated_at = Column(DateTime, default=_get_local_time, onupdate=_get_local_time, nullable=False)
