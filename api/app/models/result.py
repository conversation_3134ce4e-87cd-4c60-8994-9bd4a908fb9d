from __future__ import annotations
from datetime import datetime, timezone, timedelta
from typing import Optional
import os

from sqlalchemy import Column, Integer, String, DateTime, JSON

from ..db import Base


class Result(Base):
    __tablename__ = "results"

    id = Column(Integer, primary_key=True, index=True)
    asset_number = Column(String(128), index=True, nullable=True)
    mac = Column(String(32), index=True, nullable=True)
    profile_name = Column(String(128), nullable=True)
    run_id = Column(String(64), index=True, nullable=True)
    category = Column(String(64), nullable=True)
    test_name = Column(String(128), nullable=False)
    status = Column(String(32), nullable=False)
    metrics = Column(JSON, nullable=True)
    operator = Column(String(128), nullable=True)  # ← ADD THIS LINE
    started_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)

    # Use Central Time (UTC-5) for timestamps
    def _get_local_time():
        central_tz = timezone(timedelta(hours=-5))
        return datetime.now(central_tz).replace(tzinfo=None)
    
    created_at = Column(DateTime, default=_get_local_time, nullable=False)
    updated_at = Column(DateTime, default=_get_local_time, onupdate=_get_local_time, nullable=False)
