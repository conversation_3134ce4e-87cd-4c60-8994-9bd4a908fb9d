from __future__ import annotations
from datetime import datetime
from typing import Optional, Any, Dict, List

from pydantic import BaseModel, Field


class WipeIn(BaseModel):
    asset_number: Optional[str] = None
    mac: Optional[str] = None
    run_id: Optional[str] = None

    drive_model: Optional[str] = None
    drive_serial: Optional[str] = None
    drive_wwn: Optional[str] = None
    drive_size_bytes: Optional[int] = None
    interface: Optional[str] = Field(default=None, description="SATA|NVMe|USB|SAS|...")

    method_standard: Optional[str] = Field(default=None, description="e.g., NIST SP 800-88 Rev.1 Purge")
    method_technique: Optional[str] = Field(default=None, description="e.g., nvme format | ata-secure-erase")
    passes: Optional[int] = None

    verify_method: Optional[str] = None
    verify_result: Optional[str] = Field(default=None, pattern=r"^(passed|failed)$")
    verify_details: Optional[Dict[str, Any]] = None

    status: str = Field(default="passed", pattern=r"^(passed|failed)$")
    operator: Optional[str] = None
    location: Optional[str] = None
    notes: Optional[str] = None
    logs: Optional[List[str]] = None

    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None


class WipeOut(BaseModel):
    id: int
    asset_number: Optional[str]
    mac: Optional[str]
    run_id: Optional[str]

    drive_model: Optional[str]
    drive_serial: Optional[str]
    drive_wwn: Optional[str]
    drive_size_bytes: Optional[int]
    interface: Optional[str]

    method_standard: Optional[str]
    method_technique: Optional[str]
    passes: Optional[int]

    verify_method: Optional[str]
    verify_result: Optional[str]
    verify_details: Optional[Dict[str, Any]]

    status: str
    operator: Optional[str]
    location: Optional[str]
    notes: Optional[str]
    logs: Optional[List[str]]

    started_at: Optional[datetime]
    ended_at: Optional[datetime]

    certificate_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
