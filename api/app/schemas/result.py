from __future__ import annotations
from datetime import datetime
from typing import Any, Dict, Optional, List

from pydantic import BaseModel, Field, AliasChoices


class ResultIn(BaseModel):
    asset_number: Optional[str] = None
    mac: Optional[str] = None
    profile_name: Optional[str] = None
    run_id: Optional[str] = None
    category: Optional[str] = None
    test_name: str
    status: str = Field(pattern=r"^(passed|failed|skipped|running)$")
    metrics: Optional[Dict[str, Any]] = None
    # Accept multiple client keys and normalize to 'operator'
    operator: Optional[str] = Field(default=None, validation_alias=AliasChoices('operator', 'operator_id', 'operatorId'))
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None


class ResultOut(BaseModel):
    id: int
    asset_number: Optional[str]
    mac: Optional[str]
    profile_name: Optional[str]
    run_id: Optional[str]
    category: Optional[str]
    test_name: str
    status: str
    metrics: Optional[Dict[str, Any]]
    operator: Optional[str] = None  # ← ADD THIS LINE
    started_at: Optional[datetime]
    ended_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ResultBatchIn(BaseModel):
    run_id: Optional[str] = None
    results: List[ResultIn]
