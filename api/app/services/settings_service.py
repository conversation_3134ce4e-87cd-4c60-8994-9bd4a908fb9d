from __future__ import annotations
from sqlalchemy.orm import Session
from ..models.setting import Setting
from ..config import settings as app_settings
from ..db import engine
from sqlalchemy import text
from ..models.device import Device
from ..models.session import Session as DeviceSession
from ..models.result import Result
from ..models.wipe import Wipe


PXE_KERNEL_KEY = "pxe.kernel_url"
PXE_INITRD_KEY = "pxe.initrd_url"
PXE_ARGS_KEY = "pxe.kernel_args"

# Website integration keys
WEBSITE_BASE_URL_KEY = "website.base_url"
WEBSITE_API_KEY_KEY = "website.api_key"

# Database configuration keys
DB_MODE_KEY = "db.mode"
DB_URL_KEY = "db.url"
TIMEZONE_KEY = "timezone"
RETENTION_DAYS_KEY = "retention.days"


def get(db: Session, key: str) -> str | None:
    row = db.query(Setting).filter(Setting.key == key).one_or_none()
    return row.value if row else None


def set_(db: Session, key: str, value: str | None) -> None:
    if value is None:
        return
    row = db.query(Setting).filter(Setting.key == key).one_or_none()
    if row is None:
        row = Setting(key=key, value=value)
        db.add(row)
    else:
        row.value = value
    db.flush()


def get_pxe_settings(db: Session) -> dict:
    return {
        "kernel_url": get(db, PXE_KERNEL_KEY),
        "initrd_url": get(db, PXE_INITRD_KEY),
        "kernel_args": get(db, PXE_ARGS_KEY),
    }


def set_pxe_settings(db: Session, kernel_url: str | None, initrd_url: str | None, kernel_args: str | None) -> dict:
    set_(db, PXE_KERNEL_KEY, kernel_url)
    set_(db, PXE_INITRD_KEY, initrd_url)
    set_(db, PXE_ARGS_KEY, kernel_args)
    db.commit()
    return get_pxe_settings(db)


def get_website_settings(db: Session) -> dict:
    base_url = get(db, WEBSITE_BASE_URL_KEY)
    api_key = get(db, WEBSITE_API_KEY_KEY)
    return {
        "base_url": base_url,
        "api_key_set": bool(api_key),
    }


def set_website_settings(db: Session, base_url: str | None, api_key: str | None) -> dict:
    # Normalize base_url
    if base_url:
        base_url = base_url.strip()
        if base_url.endswith('/'):
            base_url = base_url[:-1]
    set_(db, WEBSITE_BASE_URL_KEY, base_url)
    # Store API key as-is (consider encryption at rest in future)
    if api_key:
        set_(db, WEBSITE_API_KEY_KEY, api_key.strip())
    db.commit()
    return get_website_settings(db)


def is_website_configured(db: Session) -> bool:
    ws = get_website_settings(db)
    return bool(ws.get("base_url")) and bool(ws.get("api_key_set"))


def is_pxe_configured(db: Session) -> bool:
    pxe = get_pxe_settings(db)
    return bool(pxe.get("kernel_url")) and bool(pxe.get("initrd_url"))


def get_db_settings(db: Session) -> dict:
    retention_str = get(db, RETENTION_DAYS_KEY)
    retention_days = int(retention_str) if retention_str and retention_str.isdigit() else 90  # Default to 90 days
    return {
        "db_mode": get(db, DB_MODE_KEY),
        "db_url": get(db, DB_URL_KEY),
        "timezone": get(db, TIMEZONE_KEY) or "America/Chicago",  # Default to Central time
        "retention_days": retention_days,
    }


def set_db_settings(db: Session, db_mode: str | None, db_url: str | None, timezone: str | None = None, retention_days: int | None = None) -> dict:
    set_(db, DB_MODE_KEY, db_mode)
    set_(db, DB_URL_KEY, db_url)
    if timezone:
        set_(db, TIMEZONE_KEY, timezone)
    if retention_days is not None:
        set_(db, RETENTION_DAYS_KEY, str(retention_days))
    db.commit()
    return get_db_settings(db)


def get_setup_status(db: Session) -> dict:
    website_ok = is_website_configured(db)
    pxe_ok = is_pxe_configured(db)
    return {
        "configured": bool(website_ok and pxe_ok),
        "websiteConfigured": bool(website_ok),
        "pxeConfigured": bool(pxe_ok),
    }


def clear_local_database(db: Session) -> dict:
    """Clear operational data when using local SQLite DB.

    This deletes rows from operational tables only: results, wipes, sessions, devices.
    It preserves configuration like settings, profiles, device overrides.
    """
    db_settings = get_db_settings(db)
    # Only allow when configured as local and engine is SQLite
    is_local_mode = (db_settings.get("db_mode") or "local") == "local"
    is_sqlite = str(engine.url).startswith("sqlite")
    if not (is_local_mode and is_sqlite):
        raise ValueError("Clearing database is only allowed in local SQLite mode.")

    counts: dict[str, int] = {}

    # Delete in child-to-parent order to satisfy FKs if any
    counts["results"] = db.query(Result).delete(synchronize_session=False)
    counts["wipes"] = db.query(Wipe).delete(synchronize_session=False)
    counts["sessions"] = db.query(DeviceSession).delete(synchronize_session=False)
    counts["devices"] = db.query(Device).delete(synchronize_session=False)

    db.commit()

    # For SQLite, run VACUUM to reclaim space (best-effort)
    try:
        if is_sqlite:
            with engine.connect() as conn:
                conn.execute(text("VACUUM"))
                conn.commit()
    except Exception:
        # Non-fatal
        pass

    return {"cleared": True, "counts": counts}


def clear_operational_database(db: Session) -> dict:
    """Clear operational data for supported engines (SQLite or Postgres).

    Deletes rows from operational tables only: results, wipes, sessions, devices.
    Preserves configuration like settings, profiles, device overrides.

    Returns a dict with counts per table.
    """
    db_settings = get_db_settings(db)
    url = str(engine.url)
    is_sqlite = url.startswith("sqlite")
    is_postgres = url.startswith("postgresql") or url.startswith("postgres:")

    # For historical safety, only allow SQLite if configured local, but allow Postgres regardless of mode
    if is_sqlite:
        is_local_mode = (db_settings.get("db_mode") or "local") == "local"
        if not is_local_mode:
            raise ValueError("Clearing database is only allowed in local SQLite mode.")

    if not (is_sqlite or is_postgres):
        raise ValueError("Clearing database is only supported for SQLite or Postgres.")

    counts: dict[str, int] = {}

    # Delete in child-to-parent order to satisfy FKs if any
    counts["results"] = db.query(Result).delete(synchronize_session=False)
    counts["wipes"] = db.query(Wipe).delete(synchronize_session=False)
    counts["sessions"] = db.query(DeviceSession).delete(synchronize_session=False)
    counts["devices"] = db.query(Device).delete(synchronize_session=False)

    db.commit()

    # SQLite: best-effort VACUUM
    if is_sqlite:
        try:
            with engine.connect() as conn:
                conn.execute(text("VACUUM"))
                conn.commit()
        except Exception:
            pass

    return {"cleared": True, "counts": counts}
