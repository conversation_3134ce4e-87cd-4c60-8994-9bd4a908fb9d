from __future__ import annotations
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from ..db import get_db
from ..schemas.settings import PXESettingsIn, PXESettingsOut, WebsiteSettingsIn, WebsiteSettingsOut, DatabaseSettingsIn, DatabaseSettingsOut
from ..services.settings_service import (
    get_pxe_settings,
    set_pxe_settings,
    get_website_settings,
    set_website_settings,
    get_db_settings,
    set_db_settings,
    get_setup_status,
    clear_operational_database,
)

router = APIRouter()


@router.get("/settings/pxe", response_model=PXESettingsOut)
def get_pxe(db: Session = Depends(get_db)):
    data = get_pxe_settings(db)
    return PXESettingsOut(**data)


@router.post("/settings/pxe", response_model=PXESettingsOut)
def update_pxe(payload: PXESettingsIn, db: Session = Depends(get_db)):
    data = set_pxe_settings(db, payload.kernel_url, payload.initrd_url, payload.kernel_args)
    return PXESettingsOut(**data)


@router.get("/settings/website", response_model=WebsiteSettingsOut)
def get_website(db: Session = Depends(get_db)):
    data = get_website_settings(db)
    return WebsiteSettingsOut(**data)


@router.post("/settings/website", response_model=WebsiteSettingsOut)
def update_website(payload: WebsiteSettingsIn, db: Session = Depends(get_db)):
    data = set_website_settings(db, base_url=payload.base_url, api_key=payload.api_key)
    return WebsiteSettingsOut(**data)


@router.get("/settings/database", response_model=DatabaseSettingsOut)
def get_database(db: Session = Depends(get_db)):
    data = get_db_settings(db)
    return DatabaseSettingsOut(**data)


@router.post("/settings/database", response_model=DatabaseSettingsOut)
def update_database(payload: DatabaseSettingsIn, db: Session = Depends(get_db)):
    data = set_db_settings(db, payload.db_mode, payload.db_url, payload.timezone, payload.retention_days)
    return DatabaseSettingsOut(**data)


@router.get("/setup/status")
def setup_status(db: Session = Depends(get_db)):
    return get_setup_status(db)


@router.delete("/settings/database/clear")
def clear_database(db: Session = Depends(get_db)):
    """Danger zone: Clear operational data for the active database.
    Supports SQLite (local) and Postgres. Returns counts of deleted rows.
    """
    try:
        return clear_operational_database(db)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        raise HTTPException(status_code=500, detail="Failed to clear database")
