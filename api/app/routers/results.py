from __future__ import annotations
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import Response
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.result import Result
from ..schemas.result import ResultIn, ResultOut, ResultBatchIn
from ..services.events_bus import events_bus

router = APIRouter()


@router.post("/results", response_model=ResultOut)
async def create_result(payload: ResultIn, request: Request, db: Session = Depends(get_db)):
    if not payload.test_name or not payload.status:
        raise HTTPException(status_code=422, detail="test_name and status are required")

    # Fallbacks to capture operator from alternate client keys or headers
    # Supports body keys operator/operator_id/operatorId and headers/query
    try:
        raw = await request.json()
    except Exception:
        raw = {}
    operator_fallback = (
        payload.operator
        or (raw.get('operator') if isinstance(raw, dict) else None)
        or (raw.get('operator_id') if isinstance(raw, dict) else None)
        or (raw.get('operatorId') if isinstance(raw, dict) else None)
        or request.headers.get('x-operator-id')
        or request.headers.get('operator-id')
        or request.query_params.get('operator_id')
    )

    # Strip whitespace and ensure not empty
    if operator_fallback:
        operator_fallback = operator_fallback.strip()
        if not operator_fallback:
            operator_fallback = None

    # If still missing, infer from most recent result for this asset
    if not operator_fallback and payload.asset_number:
        prev = (
            db.query(Result)
            .filter(Result.asset_number == payload.asset_number, Result.operator.isnot(None))
            .order_by(Result.created_at.desc())
            .first()
        )
        if prev:
            operator_fallback = prev.operator

    row = Result(
        asset_number=payload.asset_number,
        mac=(payload.mac or None),
        profile_name=payload.profile_name,
        run_id=payload.run_id,
        category=payload.category,
        test_name=payload.test_name,
        status=payload.status,
        metrics=payload.metrics,
        operator=operator_fallback,
        started_at=payload.started_at,
        ended_at=payload.ended_at,
    )
    db.add(row)
    db.commit()
    db.refresh(row)

    # Publish event for SSE/WebSocket consumers
    await events_bus.publish({
        "type": "result_created",
        "result": {
            "id": row.id,
            "asset_number": row.asset_number,
            "mac": row.mac,
            "profile_name": row.profile_name,
            "run_id": row.run_id,
            "category": row.category,
            "test_name": row.test_name,
            "status": row.status,
            "metrics": row.metrics,
            "operator": row.operator,  # ← ADD THIS LINE
            "started_at": row.started_at.isoformat() if row.started_at else None,
            "ended_at": row.ended_at.isoformat() if row.ended_at else None,
            "created_at": row.created_at.isoformat(),
        }
    })

    return row


@router.get("/results", response_model=List[ResultOut])
async def list_results(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    run: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    profile: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    test: Optional[str] = Query(None),
    operator: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),  # YYYY-MM-DD
    end_date: Optional[str] = Query(None),    # YYYY-MM-DD
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=1000),
    db: Session = Depends(get_db),
):
    q = db.query(Result)

    # Apply filters
    if asset:
        q = q.filter(Result.asset_number == asset)
    if mac:
        q = q.filter(Result.mac == mac)
    if run:
        q = q.filter(Result.run_id == run)
    if category:
        q = q.filter(Result.category == category)
    if profile:
        q = q.filter(Result.profile_name == profile)
    if status:
        q = q.filter(Result.status == status)
    if test:
        q = q.filter(Result.test_name.ilike(f"%{test}%"))
    if operator:
        q = q.filter(Result.operator.ilike(f"%{operator}%"))

    # Date range filters
    if start_date:
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            q = q.filter(Result.created_at >= start)
        except (ValueError, TypeError):
            pass
    if end_date:
        try:
            from datetime import datetime
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            q = q.filter(Result.created_at <= end)
        except (ValueError, TypeError):
            pass

    # Sorting
    sort_col = getattr(Result, sort_by, Result.created_at)
    if sort_order.lower() == 'asc':
        q = q.order_by(sort_col.asc())
    else:
        q = q.order_by(sort_col.desc())

    # Pagination
    rows = q.offset((page - 1) * limit).limit(limit).all()
    return rows


@router.get("/results/latest", response_model=List[ResultOut])
async def latest_results(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    db: Session = Depends(get_db),
):
    q = db.query(Result)
    if asset:
        q = q.filter(Result.asset_number == asset)
    if mac:
        q = q.filter(Result.mac == mac)
    # pick latest per test_name
    rows = (
        q.order_by(Result.test_name.asc(), Result.created_at.desc()).all()
    )
    seen = set()
    unique: List[Result] = []
    for r in rows:
        key = (r.test_name, r.asset_number, r.mac)
        if key in seen:
            continue
        seen.add(key)
        unique.append(r)
    return unique


@router.post("/results/batch", response_model=List[ResultOut])
async def create_results_batch(payload: ResultBatchIn, request: Request, db: Session = Depends(get_db)):
    created: List[Result] = []
    # Parse raw JSON to capture per-item operator variations
    try:
        raw = await request.json()
    except Exception:
        raw = {}
    # Header/query fallback if not present per item
    batch_operator_fallback = (
        request.headers.get('x-operator-id')
        or request.headers.get('operator-id')
        or request.query_params.get('operator_id')
    )
    raw_list = raw.get('results') if isinstance(raw, dict) and isinstance(raw.get('results'), list) else []
    for idx, item in enumerate(payload.results):
        raw_item = raw_list[idx] if idx < len(raw_list) and isinstance(raw_list[idx], dict) else {}
        item_op_raw = raw_item.get('operator') or raw_item.get('operator_id') or raw_item.get('operatorId')

        # Determine operator with fallbacks and inference
        item_operator = item.operator or item_op_raw or batch_operator_fallback
        if not item_operator and item.asset_number:
            prev = (
                db.query(Result)
                .filter(Result.asset_number == item.asset_number, Result.operator.isnot(None))
                .order_by(Result.created_at.desc())
                .first()
            )
            if prev:
                item_operator = prev.operator

        # Basic validation
        if not item.test_name or not item.status:
            raise HTTPException(status_code=422, detail="Each result must include test_name and status")
 
        row = Result(
            asset_number=item.asset_number,
            mac=(item.mac or None),
            profile_name=item.profile_name,
            run_id=item.run_id or payload.run_id,
            category=item.category,
            test_name=item.test_name,
            status=item.status,
            metrics=item.metrics,
            operator=item_operator,
            started_at=item.started_at,
            ended_at=item.ended_at,
        )
        db.add(row)
        created.append(row)
    db.commit()
    for row in created:
        db.refresh(row)
    # Publish events
    for row in created:
        await events_bus.publish({
            "type": "result_created",
            "result": {
                "id": row.id,
                "asset_number": row.asset_number,
                "mac": row.mac,
                "profile_name": row.profile_name,
                "run_id": row.run_id,
                "category": row.category,
                "test_name": row.test_name,
                "status": row.status,
                "metrics": row.metrics,
                "operator": row.operator,  # ← ADD THIS LINE
                "started_at": row.started_at.isoformat() if row.started_at else None,
                "ended_at": row.ended_at.isoformat() if row.ended_at else None,
                "created_at": row.created_at.isoformat(),
            }
        })
    return created


@router.get("/results/run/{run_id}", response_model=List[ResultOut])
async def results_for_run(run_id: str, db: Session = Depends(get_db)):
    rows = (
        db.query(Result)
        .filter(Result.run_id == run_id)
        .order_by(Result.created_at.desc())
        .all()
    )
    return rows


@router.delete("/results/{result_id}", response_model=dict)
async def delete_result(result_id: int, db: Session = Depends(get_db)):
    result = db.query(Result).filter(Result.id == result_id).first()
    if not result:
        raise HTTPException(status_code=404, detail="Result not found")

    db.delete(result)
    db.commit()

    # Publish event for SSE consumers
    await events_bus.publish({
        "type": "result_deleted",
        "result": {
            "id": result.id,
            "asset_number": result.asset_number,
            "test_name": result.test_name,
            "status": result.status
        }
    })

    return {"message": "Result deleted successfully"}


@router.get("/results/stats")
async def results_statistics(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),  # YYYY-MM-DD
    end_date: Optional[str] = Query(None),    # YYYY-MM-DD
    db: Session = Depends(get_db),
):
    from sqlalchemy import func

    base_query = db.query(Result)

    # Apply filters
    if asset:
        base_query = base_query.filter(Result.asset_number == asset)
    if mac:
        base_query = base_query.filter(Result.mac == mac)
    if start_date:
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            base_query = base_query.filter(Result.created_at >= start)
        except (ValueError, TypeError):
            pass
    if end_date:
        try:
            from datetime import datetime
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            base_query = base_query.filter(Result.created_at <= end)
        except (ValueError, TypeError):
            pass

    # Get totals by status
    status_counts = (
        base_query
        .with_entities(Result.status, func.count(Result.id))
        .group_by(Result.status)
        .all()
    )

    # Get unique assets tested
    unique_assets = (
        base_query
        .with_entities(Result.asset_number)
        .filter(Result.asset_number.isnot(None))
        .distinct()
        .count()
    )

    # Get recent activity (last 24 hours)
    from datetime import timedelta, datetime
    recent = datetime.now() - timedelta(hours=24)
    recent_count = (
        base_query
        .filter(Result.created_at >= recent)
        .count()
    )

    # Top tests by frequency
    top_tests = (
        base_query
        .with_entities(Result.test_name, func.count(Result.id))
        .group_by(Result.test_name)
        .order_by(func.count(Result.id).desc())
        .limit(10)
        .all()
    )

    return {
        "total_count": base_query.count(),
        "status_breakdown": {status: count for status, count in status_counts},
        "unique_assets": unique_assets,
        "recent_activity": recent_count,
        "top_tests": [{"test_name": test, "count": count} for test, count in top_tests],
    }
