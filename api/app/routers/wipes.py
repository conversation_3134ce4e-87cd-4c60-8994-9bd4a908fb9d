from __future__ import annotations
from datetime import datetime
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.wipe import Wipe
from ..schemas.wipe import WipeIn, WipeOut
from ..services.events_bus import events_bus

router = APIRouter()


def _generate_certificate_id() -> str:
    return uuid.uuid4().hex[:16].upper()


@router.post("/wipes", response_model=WipeOut)
async def create_wipe(payload: WipeIn, db: Session = Depends(get_db)):
    # Minimal validation
    if not (payload.asset_number or payload.mac or payload.drive_serial):
        raise HTTPException(status_code=422, detail="Provide at least one identifier: asset_number, mac, or drive_serial")

    row = Wipe(
        asset_number=payload.asset_number,
        mac=(payload.mac or None),
        run_id=payload.run_id,
        drive_model=payload.drive_model,
        drive_serial=payload.drive_serial,
        drive_wwn=payload.drive_wwn,
        drive_size_bytes=payload.drive_size_bytes,
        interface=payload.interface,
        method_standard=payload.method_standard,
        method_technique=payload.method_technique,
        passes=payload.passes,
        verify_method=payload.verify_method,
        verify_result=payload.verify_result,
        verify_details=payload.verify_details,
        status=payload.status,
        operator=payload.operator,
        location=payload.location,
        notes=payload.notes,
        logs=payload.logs,
        started_at=payload.started_at,
        ended_at=payload.ended_at,
        certificate_id=_generate_certificate_id(),
    )
    db.add(row)
    db.commit()
    db.refresh(row)

    await events_bus.publish({
        "type": "wipe_created",
        "wipe": {
            "id": row.id,
            "certificate_id": row.certificate_id,
            "asset_number": row.asset_number,
            "mac": row.mac,
            "run_id": row.run_id,
            "drive_serial": row.drive_serial,
            "status": row.status,
            "started_at": row.started_at.isoformat() if row.started_at else None,
            "ended_at": row.ended_at.isoformat() if row.ended_at else None,
            "created_at": row.created_at.isoformat(),
        }
    })

    return row


@router.get("/wipes", response_model=List[WipeOut])
async def list_wipes(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    run: Optional[str] = Query(None),
    serial: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    method: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),  # YYYY-MM-DD
    end_date: Optional[str] = Query(None),    # YYYY-MM-DD
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=1000),
    db: Session = Depends(get_db),
):
    q = db.query(Wipe)

    # Apply filters
    if asset:
        q = q.filter(Wipe.asset_number == asset)
    if mac:
        q = q.filter(Wipe.mac == mac)
    if run:
        q = q.filter(Wipe.run_id == run)
    if serial:
        q = q.filter(Wipe.drive_serial == serial)
    if status:
        q = q.filter(Wipe.status == status)
    if method:
        q = q.filter((Wipe.method_standard.ilike(f"%{method}%")) | (Wipe.method_technique.ilike(f"%{method}%")))

    # Date range filters
    if start_date:
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            q = q.filter(Wipe.created_at >= start)
        except (ValueError, TypeError):
            pass
    if end_date:
        try:
            from datetime import datetime
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            q = q.filter(Wipe.created_at <= end)
        except (ValueError, TypeError):
            pass

    # Sorting
    sort_col = getattr(Wipe, sort_by, Wipe.created_at)
    if sort_order.lower() == 'asc':
        q = q.order_by(sort_col.asc())
    else:
        q = q.order_by(sort_col.desc())

    # Pagination
    rows = q.offset((page - 1) * limit).limit(limit).all()
    return rows


@router.get("/wipes/{wipe_id}", response_model=WipeOut)
async def get_wipe(wipe_id: int, db: Session = Depends(get_db)):
    row = db.query(Wipe).filter(Wipe.id == wipe_id).first()
    if not row:
        raise HTTPException(status_code=404, detail="Wipe not found")
    return row


def _render_certificate_html(w: Wipe) -> str:
    # Simple branded certificate HTML
    issued = w.created_at.strftime("%Y-%m-%d %H:%M:%S UTC") if w.created_at else ""
    started = w.started_at.strftime("%Y-%m-%d %H:%M:%S UTC") if w.started_at else ""
    ended = w.ended_at.strftime("%Y-%m-%d %H:%M:%S UTC") if w.ended_at else ""
    size_gb = f"{(w.drive_size_bytes or 0) / (1024**3):.2f} GB" if w.drive_size_bytes else "Unknown"
    verify = w.verify_result or "N/A"

    return f"""
<!DOCTYPE html>
<html lang=\"en\">
<head>
<meta charset=\"utf-8\" />
<title>Nexus Wipe Certificate {w.certificate_id}</title>
<style>
  body {{ font-family: Arial, Helvetica, sans-serif; margin: 24px; color: #111; }}
  .header {{ display:flex; align-items:center; justify-content:space-between; }}
  .brand {{ font-size: 20px; font-weight: 700; letter-spacing: 0.5px; }}
  .title {{ margin-top: 8px; font-size: 28px; font-weight:700; }}
  .meta, .grid {{ margin-top: 16px; }}
  .grid {{ display: grid; grid-template-columns: 280px 1fr; gap: 8px 16px; }}
  .label {{ color: #555; }}
  .value {{ color: #000; font-weight: 600; }}
  .section {{ margin-top: 24px; }}
  .muted {{ color: #666; font-size: 12px; }}
  .badge {{ display:inline-block; padding: 4px 10px; border-radius: 12px; background: {('#16a34a' if w.status=='passed' else '#dc2626')}; color:#fff; font-weight:700; }}
  hr {{ border: none; border-top: 1px solid #e5e7eb; margin: 24px 0; }}
</style>
</head>
<body>
  <div class=\"header\">
    <div class=\"brand\">Nexus</div>
    <div class=\"badge\">{w.status.upper()}</div>
  </div>
  <div class=\"title\">Drive Wipe Certificate</div>
  <div class=\"meta\">
    <div>Certificate ID: <strong>{w.certificate_id}</strong></div>
    <div class=\"muted\">Issued: {issued}</div>
  </div>
  <hr />

  <div class=\"section\">
    <div class=\"grid\">
      <div class=\"label\">Asset Number</div><div class=\"value\">{w.asset_number or ''}</div>
      <div class=\"label\">MAC Address</div><div class=\"value\">{w.mac or ''}</div>
      <div class=\"label\">Run ID</div><div class=\"value\">{w.run_id or ''}</div>
    </div>
  </div>

  <div class=\"section\">
    <h3>Drive</h3>
    <div class=\"grid\">
      <div class=\"label\">Model</div><div class=\"value\">{w.drive_model or ''}</div>
      <div class=\"label\">Serial</div><div class=\"value\">{w.drive_serial or ''}</div>
      <div class=\"label\">WWN</div><div class=\"value\">{w.drive_wwn or ''}</div>
      <div class=\"label\">Interface</div><div class=\"value\">{w.interface or ''}</div>
      <div class=\"label\">Size</div><div class=\"value\">{size_gb}</div>
    </div>
  </div>

  <div class=\"section\">
    <h3>Wipe Method</h3>
    <div class=\"grid\">
      <div class=\"label\">Standard</div><div class=\"value\">{w.method_standard or ''}</div>
      <div class=\"label\">Technique</div><div class=\"value\">{w.method_technique or ''}</div>
      <div class=\"label\">Passes</div><div class=\"value\">{w.passes or ''}</div>
    </div>
  </div>

  <div class=\"section\">
    <h3>Verification</h3>
    <div class=\"grid\">
      <div class=\"label\">Method</div><div class=\"value\">{w.verify_method or ''}</div>
      <div class=\"label\">Result</div><div class=\"value\">{verify}</div>
    </div>
  </div>

  <div class=\"section\">
    <h3>Timeline</h3>
    <div class=\"grid\">
      <div class=\"label\">Started</div><div class=\"value\">{started}</div>
      <div class=\"label\">Ended</div><div class=\"value\">{ended}</div>
    </div>
  </div>

  <div class=\"section\">
    <h3>Operator</h3>
    <div class=\"grid\">
      <div class=\"label\">Operator</div><div class=\"value\">{w.operator or ''}</div>
      <div class=\"label\">Location</div><div class=\"value\">{w.location or ''}</div>
    </div>
  </div>

  <div class=\"section muted\">
    This certificate attests that the drive identified above was wiped using the stated method. All times are UTC. Generated by Nexus.
  </div>
</body>
</html>
"""


@router.get("/wipes/{wipe_id}/certificate.html", response_class=HTMLResponse)
async def wipe_certificate_html(wipe_id: int, db: Session = Depends(get_db)):
    w = db.query(Wipe).filter(Wipe.id == wipe_id).first()
    if not w:
        raise HTTPException(status_code=404, detail="Wipe not found")
    html = _render_certificate_html(w)
    return HTMLResponse(content=html, media_type="text/html; charset=utf-8")


@router.delete("/wipes/{wipe_id}", response_model=dict)
async def delete_wipe(wipe_id: int, db: Session = Depends(get_db)):
    wipe = db.query(Wipe).filter(Wipe.id == wipe_id).first()
    if not wipe:
        raise HTTPException(status_code=404, detail="Wipe not found")

    db.delete(wipe)
    db.commit()

    # Publish event for SSE consumers
    await events_bus.publish({
        "type": "wipe_deleted",
        "wipe": {
            "id": wipe.id,
            "certificate_id": wipe.certificate_id,
            "asset_number": wipe.asset_number,
            "drive_serial": wipe.drive_serial,
            "status": wipe.status
        }
    })

    return {"message": "Wipe deleted successfully"}


@router.get("/wipes/stats")
async def wipes_statistics(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),  # YYYY-MM-DD
    end_date: Optional[str] = Query(None),    # YYYY-MM-DD
    db: Session = Depends(get_db),
):
    from sqlalchemy import func

    base_query = db.query(Wipe)

    # Apply filters
    if asset:
        base_query = base_query.filter(Wipe.asset_number == asset)
    if mac:
        base_query = base_query.filter(Wipe.mac == mac)
    if start_date:
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            base_query = base_query.filter(Wipe.created_at >= start)
        except (ValueError, TypeError):
            pass
    if end_date:
        try:
            from datetime import datetime
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            base_query = base_query.filter(Wipe.created_at <= end)
        except (ValueError, TypeError):
            pass

    # Get totals by status
    status_counts = (
        base_query
        .with_entities(Wipe.status, func.count(Wipe.id))
        .group_by(Wipe.status)
        .all()
    )

    # Get unique drives wiped
    unique_drives = (
        base_query
        .with_entities(Wipe.drive_serial)
        .filter(Wipe.drive_serial.isnot(None))
        .distinct()
        .count()
    )

    # Get recent activity (last 24 hours)
    from datetime import timedelta, datetime
    recent = datetime.now() - timedelta(hours=24)
    recent_count = (
        base_query
        .filter(Wipe.created_at >= recent)
        .count()
    )

    # Top wipe methods by frequency
    top_methods = (
        base_query
        .with_entities(Wipe.method_standard, func.count(Wipe.id))
        .group_by(Wipe.method_standard)
        .order_by(func.count(Wipe.id).desc())
        .limit(10)
        .all()
    )

    return {
        "total_count": base_query.count(),
        "status_breakdown": {status: count for status, count in status_counts},
        "unique_drives": unique_drives,
        "recent_activity": recent_count,
        "top_methods": [{"method": method, "count": count} for method, count in top_methods],
    }
