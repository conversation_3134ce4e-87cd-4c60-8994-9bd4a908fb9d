from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from .db import init_db, SessionLocal
from .routers import health, devices, settings, ipxe, profiles, results, events
from .routers import wipes
from .routers import credits
from .routers import admin
from .routers import device_overrides
from .services.profiles_service import seed_default_profiles

app = FastAPI(title="Nexus API", version="0.1.0")

# CORS for dev; restrict in production
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Routers
app.include_router(health.router, prefix="/api", tags=["health"])
app.include_router(devices.router, prefix="/api", tags=["devices"])
app.include_router(settings.router, prefix="/api", tags=["settings"])
app.include_router(profiles.router, prefix="/api", tags=["profiles"])
app.include_router(results.router, prefix="/api", tags=["results"])
app.include_router(wipes.router, prefix="/api", tags=["wipes"])
app.include_router(events.router, prefix="/api", tags=["events"])
app.include_router(device_overrides.router, prefix="/api", tags=["device_overrides"])
app.include_router(ipxe.router, tags=["ipxe"])  # exposed at /ipxe
app.include_router(admin.router, prefix="/api", tags=["admin"])
app.include_router(credits.router, prefix="/api", tags=["credits"])


@app.on_event("startup")
def on_startup():
    init_db()
    # Seed default profiles if none exist
    db = SessionLocal()
    try:
        seed_default_profiles(db)
    finally:
        db.close()
