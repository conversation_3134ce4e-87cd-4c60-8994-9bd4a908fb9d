import os
from sqlalchemy import create_engine, text


DEFAULT_DB_URL = "sqlite:///./nexus.db"


def get_db_config():
    TEMP_CONNECT_ARGS = {"check_same_thread": False}
    temp_engine = create_engine(DEFAULT_DB_URL, echo=False, future=True, connect_args=TEMP_CONNECT_ARGS)
    db_mode, db_url = 'local', None
    try:
        with temp_engine.connect() as conn:
            # Create settings table if it doesn't exist (first run)
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(128) NOT NULL UNIQUE,
                    value TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            conn.execute(text("CREATE INDEX IF NOT EXISTS ix_settings_key ON settings(key)"))
            conn.commit()

            # Get db_mode
            result = conn.execute(text("SELECT value FROM settings WHERE key = 'db.mode'"))
            row = result.fetchone()
            db_mode = row[0] if row else 'local'

            # Get db_url
            result = conn.execute(text("SELECT value FROM settings WHERE key = 'db.url'"))
            row = result.fetchone()
            db_url = row[0] if row else None
    except Exception as e:
        # If any error (e.g. database locked), fall back to defaults
        pass
    return db_mode, db_url


db_mode, db_url = get_db_config()
if db_mode == 'external' and db_url:
    DB_URL = db_url
else:
    DB_URL = os.getenv("NEXUS_DB_URL", DEFAULT_DB_URL)


class Settings:
    DB_URL: str = DB_URL
    HOSTNAME: str = os.getenv("NEXUS_HOSTNAME", "nexus.lan")


settings = Settings()
