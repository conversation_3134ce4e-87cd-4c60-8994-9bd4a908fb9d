import { fetchData } from './utils.js';

/**
 * Modern System Info Renderer with enhanced visuals
 */
export class ModernSystemInfo {
    constructor() {
        this.container = null;
        this.refreshInterval = null;
    }

    /**
     * Initialize the modern system info component
     */
    async init() {
        this.container = document.getElementById('system-info-content');
        if (!this.container) {
            console.error('System info content area not found.');
            return;
        }
        
        await this.loadSystemInfo();
        //this.startAutoRefresh();
    }

    /**
     * Format bytes to human readable format
     */
    formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    /**
     * Create a system info card
     */
    createInfoCard(category, label, value, status = null) {
        const iconClass = `icon-${category}`;
        let statusBadge = '';
        
        if (status) {
            const statusClass = status === 'ok' ? 'ok' : status === 'warning' ? 'warning' : 'error';
            statusBadge = `<span class="status-badge status-badge--${statusClass}">${status}</span>`;
        }

        return `
            <div class="sysinfo-card sysinfo-card--${category}">
                <span class="sysinfo-card__icon ${iconClass}"></span>
                <div class="sysinfo-card__label">${label}</div>
                <div class="sysinfo-card__value">${value}</div>
                ${statusBadge}
            </div>
        `;
    }

    /**
     * Format disk information with modern styling
     */
    formatDiskInfo(disks) {
        if (!disks || disks.length === 0) {
            return this.createInfoCard('disk', 'Storage', 'No disks detected');
        }

        const diskItems = disks.map((disk, idx) => {
            const smartStatus = disk.smart_passed === null || disk.smart_passed === undefined
                ? 'unknown'
                : disk.smart_passed ? 'ok' : 'error';
            
            const smartBadge = smartStatus === 'ok' 
                ? '<span class="status-badge status-badge--ok">SMART OK</span>'
                : smartStatus === 'error'
                ? '<span class="status-badge status-badge--error">SMART FAIL</span>'
                : '<span class="status-badge status-badge--warning">SMART ?</span>';

            return `
                <div class="disk-item">
                    <span class="disk-item__name">Drive ${idx}</span>
                    <span class="disk-item__details">
                        ${disk.model || 'Unknown'} • ${disk.size_gb || '?'} GB
                        ${smartBadge}
                    </span>
                </div>
            `;
        }).join('');

        const totalSize = disks.reduce((sum, disk) => sum + (disk.size_gb || 0), 0);
        
        return `
            <div class="sysinfo-card sysinfo-card--disk">
                <span class="sysinfo-card__icon icon-disk"></span>
                <div class="sysinfo-card__label">Storage Devices</div>
                <div class="sysinfo-card__value">
                    ${disks.length} disk${disks.length !== 1 ? 's' : ''} • 
                    <span class="sysinfo-card__value--highlight">${totalSize} GB</span> total
                </div>
                <div class="disk-info-grid">
                    ${diskItems}
                </div>
            </div>
        `;
    }

    /**
     * Format battery information
     */
    formatBatteryInfo(battery) {
        if (!battery || !battery.present) {
            return this.createInfoCard('battery', 'Battery', 'No battery detected');
        }

        const batteryPercent = battery.percent || 0;
        const batteryStatus = battery.status || 'Unknown';
        const health = battery.health || 'Unknown';
        const cycles = battery.cycle_count || 'N/A';
        
        let statusClass = 'ok';
        if (batteryPercent < 20) statusClass = 'error';
        else if (batteryPercent < 40) statusClass = 'warning';

        const batteryBar = `
            <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.1); border-radius: 2px; margin-top: 0.5rem;">
                <div style="width: ${batteryPercent}%; height: 100%; background: ${batteryPercent > 40 ? '#4ae287' : batteryPercent > 20 ? '#ff9800' : '#f44336'}; border-radius: 2px; transition: width 0.3s;"></div>
            </div>
        `;

        return `
            <div class="sysinfo-card sysinfo-card--battery">
                <span class="sysinfo-card__icon icon-battery"></span>
                <div class="sysinfo-card__label">Battery</div>
                <div class="sysinfo-card__value">
                    <span class="sysinfo-card__value--highlight">${batteryPercent}%</span> • 
                    ${batteryStatus}
                </div>
                ${batteryBar}
                <div style="font-size: 0.75rem; color: #8892b0; margin-top: 0.3rem;">
                    Health: ${health} • Cycles: ${cycles}
                </div>
            </div>
        `;
    }

    /**
     * Render modern system info
     */
    renderSystemInfo(info) {
        if (!info) {
            this.container.innerHTML = `
                <div class="sysinfo sysinfo--error">
                    <span class="loading-spinner"></span>
                    Could not load system information
                </div>
            `;
            return;
        }

        const cards = [];

        // Serial Number / System ID
        if (info.serial_number) {
            cards.push(this.createInfoCard('id', 'Serial Number', info.serial_number));
        }

        // CPU
        if (info.cpu) {
            const cpuInfo = `${info.cpu}`;
            cards.push(this.createInfoCard('cpu', 'Processor', cpuInfo));
        }

        // Memory
        if (info.memory) {
            const memInfo = info.memory.replace('GB (Available:', 'GB • Available:').replace('GB)', 'GB');
            cards.push(this.createInfoCard('ram', 'Memory', memInfo, 'ok'));
        }

        // Graphics
        if (info.gpus && info.gpus.length > 0) {
            const gpuInfo = info.gpus.join(', ');
            cards.push(this.createInfoCard('gpu', 'Graphics', gpuInfo));
        }

        // Display
        if (info.screen_resolution) {
            cards.push(this.createInfoCard('display', 'Display', info.screen_resolution));
        }

        // Battery
        const batteryCard = this.formatBatteryInfo(info.battery);
        cards.push(batteryCard);

        // Disks
        const diskCard = this.formatDiskInfo(info.disks);
        cards.push(diskCard);

        this.container.innerHTML = `
            <div class="modern-sysinfo">
                ${cards.join('')}
            </div>
        `;

        // Add animation to cards
        setTimeout(() => {
            this.container.querySelectorAll('.sysinfo-card').forEach((card, index) => {
                card.style.animation = `slideIn 0.3s ${index * 0.05}s ease-out forwards`;
                card.style.opacity = '0';
                setTimeout(() => {
                    card.style.opacity = '1';
                }, index * 50);
            });
        }, 10);
    }

    /**
     * Load system information from backend
     */
    async loadSystemInfo() {
        try {
            this.container.innerHTML = '<div class="loading-spinner" style="margin: 2rem auto; display: block;"></div>';
            const data = await fetchData('/api/system_info');
            this.renderSystemInfo(data);
        } catch (error) {
            console.error('Failed to load system info:', error);
            this.container.innerHTML = `
                <div class="sysinfo sysinfo--error">
                    Failed to load system information
                </div>
            `;
        }
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh(interval = 30000) {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        this.refreshInterval = setInterval(() => {
            this.loadSystemInfo();
        }, interval);
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Export for use in main.js
export async function initModernSystemInfo() {
    const modernSysInfo = new ModernSystemInfo();
    await modernSysInfo.init();
    return modernSysInfo;
}
