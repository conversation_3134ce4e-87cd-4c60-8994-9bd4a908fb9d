import { fetchData, postData } from './utils.js';

/**
 * Modern Device Conditions Manager with enhanced UI
 */
export class ModernDeviceConditions {
    constructor() {
        this.container = null;
        this.formData = {};
        this.currentAsset = null;
    }

    /**
     * Initialize the modern device conditions component
     */
    async init() {
        this.container = document.getElementById('dc-form-area');
        if (!this.container) {
            console.error('Device conditions form area not found.');
            return;
        }

        await this.loadDeviceConditions();
        this.setupEventListeners();
    }

    /**
     * Create category card for device conditions
     */
    createCategoryCard(categoryName, fields) {
        const iconMap = {
            'Physical Condition': 'physical',
            'Case Condition': 'physical',
            'Screen Condition': 'display',
            'Keyboard Condition': 'keyboard',
            'Touchpad Condition': 'touchpad',
            'USB Ports': 'ports',
            'Missing Items & Grade': 'checklist',
            'Notes': 'notes'
        };

        const icon = iconMap[categoryName] || 'physical';
        
        const fieldsHtml = Object.entries(fields).map(([fieldName, fieldConfig]) => {
            return this.createField(categoryName, fieldName, fieldConfig);
        }).join('');

        return `
            <div class="dc-category-card" data-category="${categoryName}">
                <div class="dc-category-card__title">
                    <span class="dc-category-card__icon icon-${icon}"></span>
                    ${categoryName}
                </div>
                <div class="dc-category-card__fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Create individual field
     */
    createField(category, fieldName, config) {
        const fieldId = `dc-${category.replace(/\s+/g, '-')}-${fieldName}`;
        const value = this.formData[fieldName] || config.default || '';
        
        let inputHtml = '';
        
        if (config.type === 'select' && config.options) {
            const options = config.options.map(opt => 
                `<option value="${opt}" ${value === opt ? 'selected' : ''}>${opt}</option>`
            ).join('');
            
            inputHtml = `
                <select id="${fieldId}" name="${fieldName}" 
                        class="dc-field__input dc-field__input--select"
                        data-field="${fieldName}">
                    ${options}
                </select>
            `;
        } else if (config.type === 'multiselect' && config.options) {
            const checkboxes = config.options.map(opt => {
                const isChecked = Array.isArray(value) && value.includes(opt);
                return `
                    <label class="dc-checkbox-label">
                        <input type="checkbox" 
                               name="${fieldName}" 
                               value="${opt}"
                               ${isChecked ? 'checked' : ''}
                               data-field="${fieldName}">
                        <span>${opt}</span>
                    </label>
                `;
            }).join('');
            
            inputHtml = `
                <div class="dc-checkbox-group">
                    ${checkboxes}
                </div>
            `;
        } else if (config.type === 'textarea') {
            inputHtml = `
                <textarea id="${fieldId}" 
                          name="${fieldName}" 
                          class="dc-field__input dc-field__input--textarea"
                          rows="3"
                          placeholder="${config.placeholder || ''}"
                          data-field="${fieldName}">${value}</textarea>
            `;
        } else {
            inputHtml = `
                <input type="${config.type || 'text'}" 
                       id="${fieldId}" 
                       name="${fieldName}"
                       class="dc-field__input"
                       value="${value}"
                       placeholder="${config.placeholder || ''}"
                       data-field="${fieldName}">
            `;
        }

        return `
            <div class="dc-field" data-field="${fieldName}">
                <label class="dc-field__label" for="${fieldId}">
                    ${fieldName}
                </label>
                ${inputHtml}
            </div>
        `;
    }

    /**
     * Load device conditions schema and current values
     */
    async loadDeviceConditions(assetNumber = null) {
        try {
            // Show loading state
            this.container.innerHTML = '<div class="loading-spinner" style="margin: 2rem auto; display: block;"></div>';
            
            // Use hardcoded schema that matches the existing structure
            const schema = {
                "Case Condition": { 
                    type: "select", 
                    options: ["Good", "Light Scratches", "Heavy Scratches", "Light Dents", "Heavy Dents", "Cracks"],
                    default: "Good"
                },
                "Screen Condition": { 
                    type: "select", 
                    options: ["Good", "Light Scratches", "Heavy Scratches", "Cracked", "Pressure Marks", "Faded Areas", "Dead Pixels"],
                    default: "Good"
                },
                "Keyboard Condition": { 
                    type: "select", 
                    options: ["Good", "Light Wear", "Heavy Wear"],
                    default: "Good"
                },
                "Touchpad Condition": { 
                    type: "select", 
                    options: ["Good", "Light Wear", "Heavy Wear"],
                    default: "Good"
                },
                "USB Ports": { 
                    type: "select", 
                    options: ["Good", "Damaged"],
                    default: "Good"
                },
                "Grade": { 
                    type: "select", 
                    options: ["A", "B", "C", "Damaged"],
                    default: "A"
                },
                "Missing Items": { 
                    type: "multiselect", 
                    options: ["N/A", "Point Stick", "Footpads", "Keyboard Keys", "Bottom Screws"],
                    default: ["N/A"]
                },
                "Notes": { 
                    type: "textarea", 
                    placeholder: "Notes",
                    default: ""
                }
            };

            // Load existing values if asset number provided
            if (assetNumber) {
                const existingData = await fetchData(`/api/device_conditions/${assetNumber}`);
                if (existingData && existingData.conditions) {
                    this.formData = existingData.conditions;
                    this.currentAsset = assetNumber;
                }
            } else {
                // Try to get asset number from input field
                const assetInput = document.getElementById('asset-number');
                if (assetInput && assetInput.value) {
                    const existingData = await fetchData(`/api/device_conditions/${assetInput.value}`);
                    if (existingData && existingData.conditions) {
                        this.formData = existingData.conditions;
                        this.currentAsset = assetInput.value;
                    }
                }
            }

            // Render the form
            this.renderForm(schema);
            
        } catch (error) {
            console.error('Failed to load device conditions:', error);
            this.container.innerHTML = `
                <div class="sysinfo sysinfo--error">
                    Failed to load device conditions
                </div>
            `;
        }
    }

    /**
     * Render the device conditions form
     */
    renderForm(schema) {
        const fieldsHtml = Object.entries(schema).map(([fieldName, fieldConfig]) => {
            return this.createField('', fieldName, fieldConfig);
        }).join('');

        this.container.innerHTML = `
            <div class="dc-modern-form dc-modern-form--flat">
                ${fieldsHtml}
            </div>
        `;

        // Re-attach field listeners
        this.attachFieldListeners();
    }

    /**
     * Organize schema into categories
     */
    organizeSchema(schema) {
        const categories = {
            'Physical Condition': {},
            'Screen Condition': {},
            'Keyboard Condition': {},
            'Touchpad Condition': {},
            'USB Ports': {},
            'Missing Items & Grade': {},
            'Notes': {}
        };

        Object.entries(schema).forEach(([fieldName, fieldConfig]) => {
            let category = 'Notes';
            
            // Categorize based on field name
            if (fieldName.toLowerCase().includes('case') || fieldName === 'Physical Condition') {
                category = 'Physical Condition';
            } else if (fieldName.toLowerCase().includes('screen') || fieldName.toLowerCase().includes('display')) {
                category = 'Screen Condition';
            } else if (fieldName.toLowerCase().includes('keyboard')) {
                category = 'Keyboard Condition';
            } else if (fieldName.toLowerCase().includes('touchpad') || fieldName.toLowerCase().includes('trackpad')) {
                category = 'Touchpad Condition';
            } else if (fieldName.toLowerCase().includes('usb') || fieldName.toLowerCase().includes('port')) {
                category = 'USB Ports';
            } else if (fieldName.toLowerCase().includes('missing') || fieldName.toLowerCase().includes('grade')) {
                category = 'Missing Items & Grade';
            } else if (fieldName.toLowerCase().includes('notes')) {
                category = 'Notes';
            }

            categories[category][fieldName] = fieldConfig;
        });

        // Remove empty categories
        Object.keys(categories).forEach(key => {
            if (Object.keys(categories[key]).length === 0) {
                delete categories[key];
            }
        });

        return categories;
    }

    /**
     * Attach event listeners to form fields
     */
    attachFieldListeners() {
        // Regular inputs and selects
        this.container.querySelectorAll('.dc-field__input').forEach(input => {
            input.addEventListener('change', (e) => {
                const fieldName = e.target.dataset.field;
                this.formData[fieldName] = e.target.value;
            });
        });

        // Checkboxes (multiselect)
        const checkboxGroups = {};
        this.container.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            const fieldName = checkbox.dataset.field;
            if (!checkboxGroups[fieldName]) {
                checkboxGroups[fieldName] = [];
            }
            checkboxGroups[fieldName].push(checkbox);
            
            checkbox.addEventListener('change', () => {
                const selectedValues = checkboxGroups[fieldName]
                    .filter(cb => cb.checked)
                    .map(cb => cb.value);
                this.formData[fieldName] = selectedValues;
            });
        });
    }

    /**
     * Setup main event listeners
     */
    setupEventListeners() {
        // Save button
        const saveBtn = document.getElementById('save-dc-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveConditions());
        }

        // Asset number change
        const assetInput = document.getElementById('asset-number');
        if (assetInput) {
            let debounceTimer;
            assetInput.addEventListener('input', () => {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    if (assetInput.value && assetInput.value !== this.currentAsset) {
                        this.loadDeviceConditions(assetInput.value);
                    }
                }, 1000);
            });
        }
    }

    /**
     * Save device conditions
     */
    async saveConditions() {
        const assetNumber = document.getElementById('asset-number')?.value;
        if (!assetNumber) {
            this.showStatus('Please enter an asset number', 'error');
            return;
        }

        const saveBtn = document.getElementById('save-dc-btn');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = 'Saving...';
        saveBtn.disabled = true;

        try {
            const result = await postData(`/api/device_conditions/${assetNumber}`, {
                asset_number: assetNumber,
                conditions: this.formData
            });

            if (result.status === 'success') {
                this.showStatus('✓ Conditions saved successfully', 'success');
                this.currentAsset = assetNumber;
                
                // Update button temporarily
                saveBtn.textContent = '✓ Saved';
                saveBtn.classList.add('btn-modern--success');
                setTimeout(() => {
                    saveBtn.textContent = originalText;
                    saveBtn.classList.remove('btn-modern--success');
                }, 2000);
            } else {
                throw new Error(result.message || 'Failed to save conditions');
            }
        } catch (error) {
            console.error('Failed to save device conditions:', error);
            this.showStatus('Failed to save conditions', 'error');
            saveBtn.textContent = originalText;
        } finally {
            saveBtn.disabled = false;
        }
    }

    /**
     * Show status message
     */
    showStatus(message, type = 'info') {
        const statusEl = document.getElementById('dc-status');
        if (!statusEl) return;

        statusEl.className = `status-message-modern status-message-modern--${type}`;
        statusEl.textContent = message;
        statusEl.style.display = 'inline-flex';

        if (type === 'success') {
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
    }

    /**
     * Get current form data
     */
    getFormData() {
        return this.formData;
    }
}

// Export for use in main.js
export async function initModernDeviceConditions() {
    const modernDC = new ModernDeviceConditions();
    await modernDC.init();
    return modernDC;
}
