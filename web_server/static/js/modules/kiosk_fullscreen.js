export function initKiosk(options = {}) {
  const defaults = {
    allowEscToClose: false,
    suppressQuitCombos: true,
    reenterOnVisibility: true,
    stickyPointerLock: false,
    debug: false,
    // New: try windowed screen API for kiosk-capable Chromium builds (no gesture requirement)
    tryWindowFullscreen: true
  };
  const cfg = { ...defaults, ...options };

  const log = (...args) => { if (cfg.debug) console.log('[KIOSK]', ...args); };

  async function requestFsNative() {
    try {
      const el = document.documentElement;
      if (!document.fullscreenElement && el.requestFullscreen) {
        // Add smooth transition CSS before requesting fullscreen
        el.style.transition = 'all 0.3s ease-in-out';
        await el.requestFullscreen({ navigationUI: 'hide' });
        log('Entered fullscreen via requestFullscreen');
        return true;
      }
    } catch (e) {
      log('requestFullscreen failed:', e);
      // Remove transition on failure
      document.documentElement.style.transition = '';
    }
    return false;
  }

  async function requestWindowFullscreen() {
    // Chromium with --kiosk or Installed App (PWA) may allow this without a gesture
    try {
      if (cfg.tryWindowFullscreen && window.screen && typeof window.screen.orientation !== 'undefined') {
        // Best-effort: some kiosk builds allow screen.orientation.lock without user gesture
        try {
          if (window.screen.orientation && window.screen.orientation.lock) {
            await window.screen.orientation.lock('landscape');
          }
        } catch (_) { /* ignore */ }
      }
      // Use the window.fullScreen toggle detection and F11 prevention is already handled;
      // There is no standard API to force browser UI fullscreen without gesture except kiosk mode.
      // But when Chromium is launched with --kiosk or --app, the window is borderless/fullscreen already.
      // As a hint for engines that support it (Electron/Chrome App style), call window.moveTo/resizeTo.
      try {
        if (window.moveTo && window.resizeTo) {
          window.moveTo(0, 0);
          window.resizeTo(screen.availWidth || screen.width, screen.availHeight || screen.height);
        }
      } catch (_) { /* ignore */ }
    } catch (e) {
      log('requestWindowFullscreen hint failed:', e);
    }
    return false;
  }

  async function requestFs() {
    // First try native Fullscreen API; if it fails (due to gesture), rely on kiosk window sizing hints.
    const ok = await requestFsNative();
    if (!ok) {
      await requestWindowFullscreen();
    }
  }

  function preventKeys(e) {
    const key = (e.key || '').toLowerCase();
    const ctrl = e.ctrlKey || e.metaKey;
    const alt = e.altKey;

    if (key === 'f11') {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    if (!cfg.allowEscToClose && key === 'escape') {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    if (cfg.suppressQuitCombos) {
      if (ctrl && (key === 'w' || key === 'q' || key === 'f4')) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }
      if (alt && key === 'f4') {
        e.preventDefault();
        e.stopPropagation();
        return;
      }
    }
  }

  function attachGestureTriggersOnce() {
    const onFirstGesture = async () => {
      document.removeEventListener('pointerdown', onFirstGesture, true);
      document.removeEventListener('keydown', onFirstGesture, true);
      await requestFs();
    };
    document.addEventListener('pointerdown', onFirstGesture, true);
    document.addEventListener('keydown', onFirstGesture, true);
  }

  function keepFullscreen() {
    document.addEventListener('fullscreenchange', () => {
      if (!document.fullscreenElement) {
        log('Fullscreen lost (fullscreenchange)');
      }
    });
    if (cfg.reenterOnVisibility) {
      document.addEventListener('visibilitychange', async () => {
        if (document.visibilityState === 'visible') {
          await requestFs();
        }
      });
    }
    // Also try once after a short delay in case DOM is not fully ready at import time
    setTimeout(() => { requestFs(); }, 0);
  }

  function setupPointerLock() {
    if (!cfg.stickyPointerLock) return;
    const canvas = document.querySelector('canvas');
    if (!canvas || !canvas.requestPointerLock) return;

    const requestLock = () => {
      try { canvas.requestPointerLock(); } catch {}
    };
    canvas.addEventListener('click', requestLock);
    document.addEventListener('pointerlockchange', () => {
      if (document.pointerLockElement !== canvas) {
        // will reacquire on next click
      }
    });
  }

  window.addEventListener('keydown', preventKeys, true);
  window.addEventListener('keyup', preventKeys, true);
  window.addEventListener('keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Tab') {
      e.preventDefault();
      e.stopPropagation();
    }
  }, true);

  // Attempt to enter fullscreen immediately on import (works when Chromium is launched with --kiosk/--app)
  requestFs();
  // Keep a heartbeat to retry a few times in the first second to catch late-ready states in kiosk mode
  // Reduced retry count and interval for smoother experience
  let retries = 3;
  const tick = setInterval(async () => {
    if (document.fullscreenElement || retries <= 0) {
      clearInterval(tick);
      return;
    }
    await requestFs();
    retries -= 1;
  }, 150);

  attachGestureTriggersOnce();
  keepFullscreen();
  setupPointerLock();

  return {
    ensureFullscreen: requestFs
  };
}