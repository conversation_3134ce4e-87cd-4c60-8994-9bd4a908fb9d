import { fetchData } from './utils.js';

let wipeProgressInterval = null; // internal module state
let lastProgressPayload = null;   // cache last progress for certificate/export

// --- Drive Wipe Functionality (extracted from main.js) ---
export function setupDriveWipe() {
    const loadDrivesBtn = document.getElementById('load-drives-btn');
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    const startWipeBtn = document.getElementById('start-wipe-btn');
    const cancelWipeBtn = document.getElementById('cancel-wipe-btn');
    const methodInfoBtn = document.getElementById('wipe-method-info-btn');
    const copyLogBtn = document.getElementById('wipe-log-copy-btn');
    const exportLogBtn = document.getElementById('wipe-log-export-btn');
    const certBtn = document.getElementById('wipe-cert-btn');

    if (loadDrivesBtn) loadDrivesBtn.addEventListener('click', loadDrivesForWipe);
    if (startWipeBtn) startWipeBtn.addEventListener('click', startWipe);
    if (cancelWipeBtn) cancelWipeBtn.addEventListener('click', cancelWipe);
    if (methodInfoBtn) methodInfoBtn.addEventListener('click', showMethodDetails);
    if (copyLogBtn) copyLogBtn.addEventListener('click', copyLogToClipboard);
    if (exportLogBtn) exportLogBtn.addEventListener('click', exportLogToFile);
    if (certBtn) certBtn.addEventListener('click', downloadCertificate);

    if (wipeMethodSelect) {
        wipeMethodSelect.addEventListener('change', () => {
            const selectedDrives = document.querySelectorAll('input[name="selected_drive_wipe"]:checked');
            if (startWipeBtn) {
                startWipeBtn.disabled = selectedDrives.length === 0 || wipeMethodSelect.value === '' || wipeProgressInterval !== null;
            }
        });
    }

    // Automatically load/refresh drives each time the Secure Drive Wipe section is expanded
    const wipeSection = document.getElementById('drive-wipe-section');
    if (wipeSection) {
        const observer = new MutationObserver(() => {
            if (wipeSection.classList.contains('expanded')) {
                loadDrivesForWipe();
            }
        });
        observer.observe(wipeSection, { attributes: true, attributeFilter: ['class'] });
    }
}

// Quick Start and filter logic removed; all drives will be auto-selected on load.

async function showMethodDetails() {
    try {
        const select = document.getElementById('wipe-method-select');
        if (!select || !select.value) {
            alert('Select a wipe method first.');
            return;
        }
        const methods = await fetchData('/api/wipe_methods');
        const method = (methods || []).find(m => m.key === select.value);
        if (!method) {
            alert('Details not available for this method.');
            return;
        }
        const lines = [
            `Name: ${method.name || method.key}`,
            method.description ? `Description: ${method.description}` : null,
            method.compliance_tags ? `Compliance: ${method.compliance_tags.join(', ')}` : null,
            typeof method.passes === 'number' ? `Passes: ${method.passes}` : null,
            method.verify_mode ? `Verification: ${method.verify_mode}` : null,
            method.estimated_speed_MBps ? `Estimated Speed: ${method.estimated_speed_MBps} MB/s` : null,
        ].filter(Boolean);
        alert(lines.join('\n'));
    } catch (e) {
        console.error('Failed to load method details:', e);
        alert('Failed to load method details.');
    }
}

function copyLogToClipboard() {
    const el = document.getElementById('wipe-log-output');
    if (!el) return;
    const text = Array.from(el.querySelectorAll('.log-entry')).map(div => div.innerText).join('\n');
    navigator.clipboard.writeText(text).then(() => {
        addWipeLogMessage('Log copied to clipboard', 'info');
    }).catch(err => {
        console.error('Clipboard error', err);
        addWipeLogMessage('Failed to copy log', 'error');
    });
}

function exportLogToFile() {
    const el = document.getElementById('wipe-log-output');
    if (!el) return;
    const text = Array.from(el.querySelectorAll('.log-entry')).map(div => div.innerText).join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    const ts = new Date().toISOString().replace(/[:.]/g, '-');
    a.href = url;
    a.download = `crucible-wipe-log-${ts}.txt`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
}

async function downloadCertificate() {
    try {
        // Try backend endpoint first if available
        const resp = await fetch('/api/wipe_certificate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ result: lastProgressPayload || {} })
        });
        if (resp.ok) {
            const blob = await resp.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'crucible-wipe-certificate';
            document.body.appendChild(a);
            a.click();
            a.remove();
            URL.revokeObjectURL(url);
            return;
        }
        // Fallback: download JSON summary
        throw new Error('Backend certificate not available');
    } catch (e) {
        console.warn('Certificate endpoint unavailable, exporting JSON summary instead:', e);
        const data = JSON.stringify(lastProgressPayload || {}, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        const ts = new Date().toISOString().replace(/[:.]/g, '-');
        a.href = url;
        a.download = `crucible-wipe-certificate-${ts}.json`;
        document.body.appendChild(a);
        a.click();
        a.remove();
        URL.revokeObjectURL(url);
    }
}

function formatEta(seconds) {
    const s = Math.max(0, Math.floor(seconds));
    const h = Math.floor(s / 3600);
    const m = Math.floor((s % 3600) / 60);
    const sec = s % 60;
    if (h) return `${h}h ${m}m ${sec}s`;
    if (m) return `${m}m ${sec}s`;
    return `${sec}s`;
}


async function loadWipeMethods() {
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    const wipeStatusMessage = document.getElementById('wipe-status-message');
    if (!wipeMethodSelect || !wipeStatusMessage) return;

    try {
        const methods = await fetchData('/api/wipe_methods') || [];
        wipeMethodSelect.innerHTML = '<option value="">-- Select Wipe Method --</option>';
        methods.forEach(method => {
            const option = document.createElement('option');
            option.value = method.key;
            option.textContent = method.name;
            option.title = method.description;
            wipeMethodSelect.appendChild(option);
        });
        wipeMethodSelect.disabled = false;
    } catch (error) {
        console.error('Error loading wipe methods:', error);
        wipeStatusMessage.textContent = `Error loading wipe methods: ${error.message}`;
        wipeMethodSelect.disabled = true;
    }
}

async function loadDrivesForWipe() {
    const driveListContainer = document.getElementById('drive-list-container');
    const startWipeBtn = document.getElementById('start-wipe-btn');
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    if (!driveListContainer || !startWipeBtn) return;

    driveListContainer.innerHTML = '<p>Loading drives...</p>';
    startWipeBtn.disabled = true;

    try {
        const drives = await fetchData('/api/drives') || [];
        driveListContainer.innerHTML = '';

        if (drives.length === 0) {
            driveListContainer.innerHTML = '<p>No drives found or accessible.</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'info-table drive-wipe-table';
        table.innerHTML = `
            <thead><tr>
                <th>Select</th><th>Path</th><th>Model</th><th>Size (GB)</th><th>Type</th><th>Interface</th><th>Serial</th><th>Mountpoints</th><th>Status</th><th>Progress</th>
            </tr></thead>`;
        const tbody = document.createElement('tbody');
        drives.forEach(drive => {
            const row = tbody.insertRow();
            const driveId = drive.path.replace(/[^a-zA-Z0-9]/g, '_');
            row.id = `drive_row_${driveId}`;

            const cbCell = row.insertCell();
            const cb = document.createElement('input');
            cb.type = 'checkbox';
            cb.name = 'selected_drive_wipe';
            cb.value = drive.path;
            cb.id = `drive_wipe_cb_${driveId}`;
            // Warn if drive has mounted partitions but still allow selection
            if (drive.mountpoints && drive.mountpoints.length) {
                cb.title = `Mounted at: ${drive.mountpoints.join(', ')}`;
                row.classList.add('has-mountpoints');
            }
            // Mark internal/external based on heuristic (USB => external)
            const iface = (drive.interface || drive.bus || '').toUpperCase();
            if (iface.includes('USB')) row.dataset.internal = 'false'; else row.dataset.internal = 'true';
            cbCell.appendChild(cb);
            // Update recommended method and button state when selection changes
            cb.addEventListener('change', () => {
                updateRecommendedMethod();
                const selected = document.querySelectorAll('input[name="selected_drive_wipe"]:checked');
                startWipeBtn.disabled = selected.length === 0 || wipeMethodSelect.value === '' || wipeProgressInterval !== null;
            });
            row.insertCell().textContent = drive.path || 'N/A';
            row.insertCell().textContent = drive.model || 'N/A';
            row.insertCell().textContent = drive.size_gb || 'N/A';
            row.insertCell().textContent = drive.type || 'N/A';
            row.insertCell().textContent = iface || (drive.interface || '');
            row.insertCell().textContent = drive.serial || 'N/A';
            row.insertCell().textContent = (drive.mountpoints && drive.mountpoints.length) ? drive.mountpoints.join(', ') : 'None';

            // New Status Cell
            const statusCell = row.insertCell();
            statusCell.id = `drive_status_${driveId}`;
            statusCell.textContent = 'Pending';
            statusCell.className = 'drive-status-cell';

            // New Progress Bar Cell
            const progressCell = row.insertCell();
            progressCell.className = 'drive-progress-cell';
            progressCell.innerHTML = `
                <div class="progress-bar-container-small">
                    <div id="drive_progress_${driveId}" class="progress-bar-small" style="width: 0%;"></div>
                </div>
                <div class="progress-meta"><span id="drive_eta_${driveId}" class="eta-text"></span><span id="drive_rate_${driveId}" class="rate-text"></span></div>
            `;
        });
        table.appendChild(tbody);
        driveListContainer.appendChild(table);
        loadWipeMethods();
        // Auto-select ALL drives (internal and external/USB) by default
        const checkboxes = driveListContainer.querySelectorAll('input[name="selected_drive_wipe"]');
        checkboxes.forEach(cb => { cb.checked = true; });
        // Update recommendation and Start button state
        updateRecommendedMethod();
        const selected = document.querySelectorAll('input[name="selected_drive_wipe"]:checked');
        startWipeBtn.disabled = selected.length === 0 || wipeMethodSelect.value === '' || wipeProgressInterval !== null;
    } catch (error) {
        console.error('Error loading drives:', error);
        driveListContainer.innerHTML = `<p style="color:red;">${error.message}</p>`;
    }
}

async function startWipe() {
    const selected = Array.from(document.querySelectorAll('input[name="selected_drive_wipe"]:checked')).map(cb => cb.value);
    const methodKey = document.getElementById('wipe-method-select').value;
    if (!selected.length) { alert('Select at least one drive.'); return; }
    if (!methodKey) { alert('Select a wipe method.'); return; }

    // Check license status before proceeding
    try {
        const { checkLicenseForOperations } = await import('./license_status.js');
        const licenseAllowed = await checkLicenseForOperations();

        if (!licenseAllowed) {
            alert('License Required\n\nNo valid licenses available for drive wipe operations.\n\nTo purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible');
            return;
        }
    } catch (error) {
        console.warn('License check failed, proceeding anyway:', error);
    }

    // Direct start as requested (no additional safety confirmations)

    disableWipeControls(true);
    addWipeLogMessage('Initiating wipe...', 'info');

    try {
        const response = await fetch('/api/wipe_drives', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ drives: selected, method_key: methodKey })
        });
        const result = await response.json();

        if (!response.ok) {
            // Handle license-specific errors
            if (response.status === 402) {
                // License required error
                const licenseMessage = result.details || 'No valid licenses available for drive wipe operations.';
                const guidance = 'To purchase licenses:\n1. Visit the Nexus website\n2. Log in to your account\n3. Purchase credits or activate a server license\n4. Configure your API key in Crucible';
                alert(`License Required\n\n${licenseMessage}\n\n${guidance}`);
                addWipeLogMessage(`License Required: ${licenseMessage}`, 'error');
            } else if (response.status === 503 && result.error === 'License Validation Error') {
                // License validation error
                alert(`License Validation Error\n\n${result.details}\n\nPlease check your Nexus server connection.`);
                addWipeLogMessage(`License Validation Error: ${result.details}`, 'error');
            } else {
                // Other errors
                throw new Error(result.error || 'Failed to start wipe');
            }
            disableWipeControls(false, true);
            return;
        }

        addWipeLogMessage(result.message || 'Wipe process started.', 'info');
        if (wipeProgressInterval) clearInterval(wipeProgressInterval);
        wipeProgressInterval = setInterval(updateWipeProgress, 1500);
    } catch (e) {
        console.error(e);
        addWipeLogMessage(e.message, 'error');
        disableWipeControls(false, true);
    }
}

async function updateWipeProgress() {
    const overallBar = document.getElementById('wipe-progress-bar');
    const overallStatusMsg = document.getElementById('wipe-status-message');
    const overallEta = document.getElementById('wipe-overall-eta');
    const logOutput = document.getElementById('wipe-log-output');

    if (!overallBar || !overallStatusMsg || !logOutput) {
        stopPolling(true);
        return;
    }

    try {
        const data = await fetchData('/api/wipe_progress');
        lastProgressPayload = data;

        // Update overall progress
        overallBar.style.width = `${data.overall_progress || 0}%`;
        overallBar.textContent = `${Math.round(data.overall_progress || 0)}%`;
        overallStatusMsg.textContent = `Status: ${data.message || data.status}`;
        if (overallEta) {
            if (typeof data.eta_seconds === 'number') {
                overallEta.textContent = ` • ETA ${formatEta(data.eta_seconds)}`;
            } else {
                overallEta.textContent = '';
            }
        }

        // Update logs
        const existingLogs = logOutput.children.length;
        (data.logs || []).slice(existingLogs).forEach(log => {
            // Assuming log is a string like "[TIMESTAMP] [LEVEL] Message"
            const match = log.match(/\[(.*?)\] \[(.*?)\] (.*)/);
            if (match) {
                const [_, timestamp, level, message] = match;
                addWipeLogMessage(message, level.toLowerCase(), timestamp);
            } else {
                addWipeLogMessage(log); // Fallback for old format
            }
        });

        // Update individual drive statuses
        if (data.drives) {
            for (const drivePath in data.drives) {
                const driveData = data.drives[drivePath];
                const driveId = drivePath.replace(/[^a-zA-Z0-9]/g, '_');

                const statusCell = document.getElementById(`drive_status_${driveId}`);
                const progressBar = document.getElementById(`drive_progress_${driveId}`);
                const etaEl = document.getElementById(`drive_eta_${driveId}`);
                const rateEl = document.getElementById(`drive_rate_${driveId}`);

                if (statusCell) {
                    statusCell.textContent = driveData.status;
                    // Clear old status classes and add the new one
                    statusCell.className = 'drive-status-cell';
                    statusCell.classList.add(`status-${driveData.status}`);
                }
                if (progressBar) {
                    progressBar.style.width = `${driveData.progress || 0}%`;
                    // Add classes for failed/completed states for color
                    progressBar.classList.remove('status-failed', 'status-complete');
                    if (driveData.status === 'fail' || driveData.status === 'error') {
                        progressBar.classList.add('status-failed');
                    } else if (driveData.status === 'pass' || driveData.status === 'complete') {
                        progressBar.classList.add('status-complete');
                    }
                }
                if (etaEl) {
                    if (typeof driveData.eta_seconds === 'number') etaEl.textContent = `ETA ${formatEta(driveData.eta_seconds)}`; else etaEl.textContent = '';
                }
                if (rateEl) {
                    if (typeof driveData.throughput_mb_s === 'number') rateEl.textContent = ` • ${driveData.throughput_mb_s.toFixed(1)} MB/s`; else rateEl.textContent = '';
                }
            }
        }

        // Check for completion
        if (['complete', 'cancelled', 'error'].includes(data.status)) {
            stopPolling(data.status === 'error', data);
        }
    } catch (e) {
        console.error('Error updating wipe progress:', e);
        addWipeLogMessage(`Error polling for progress: ${e.message}`, 'error');
        // Consider stopping polling on error to prevent loops
        stopPolling(true);
    }
}

function addWipeLogMessage(msg, type = 'info', timestamp = null) {
    const out = document.getElementById('wipe-log-output');
    if (!out) return;
    const div = document.createElement('div');
    const time = timestamp || new Date().toLocaleTimeString();
    div.innerHTML = `<span class="log-timestamp">[${time}]</span> <span class="log-message">${msg}</span>`;
    div.className = `log-entry log-${type.toLowerCase()}`;
    out.appendChild(div);
    out.scrollTop = out.scrollHeight;
}

function displayWipeSummary(data) {
    const driveListContainer = document.getElementById('drive-list-container');
    if (!driveListContainer || !data || !data.drives) {
        driveListContainer.innerHTML = '<p>Could not display wipe summary.</p>';
        return;
    }

    let summaryHtml = '<h3>Wipe Summary</h3>';
    summaryHtml += '<table class="info-table drive-wipe-table summary-table">';
    summaryHtml += `
        <thead><tr>
            <th>Path</th><th>Method</th><th>Status</th><th>Verification</th><th>Errors</th><th>Start Time</th><th>End Time</th>
        </tr></thead>`;

    summaryHtml += '<tbody>';
    for (const drivePath in data.drives) {
        const driveData = data.drives[drivePath];
        const result = driveData.result || {};
        const statusClass = `status-${(result.status || 'unknown').toLowerCase()}`;

        summaryHtml += `
            <tr class="${statusClass}">
                <td>${drivePath}</td>
                <td>${result.method || data.method || 'N/A'}</td>
                <td class="drive-status-cell ${statusClass}">${result.status || 'Unknown'}</td>
                <td>${result.verification || 'N/A'}</td>
                <td>${result.errors || 'None'}</td>
                <td>${result.start_time ? new Date(result.start_time).toLocaleString() : 'N/A'}</td>
                <td>${result.end_time ? new Date(result.end_time).toLocaleString() : 'N/A'}</td>
            </tr>
        `;
    }
    summaryHtml += '</tbody></table>';

    driveListContainer.innerHTML = summaryHtml;
}


function stopPolling(isError = false, finalData = null) {
    if (wipeProgressInterval) {
        clearInterval(wipeProgressInterval);
        wipeProgressInterval = null;
    }
    disableWipeControls(false, isError);

    if (finalData && ['complete', 'cancelled', 'error'].includes(finalData.status)) {
        displayWipeSummary(finalData);
    }

    // Enable certificate download when completed successfully
    try {
        const certBtn = document.getElementById('wipe-cert-btn');
        if (certBtn) certBtn.disabled = !(finalData && finalData.status === 'complete');
    } catch {}
}

function disableWipeControls(disable, isError = false) {
    const start = document.getElementById('start-wipe-btn');
    const cancel = document.getElementById('cancel-wipe-btn');
    const load = document.getElementById('load-drives-btn');
    const methodSel = document.getElementById('wipe-method-select');

    if (start) start.disabled = disable;
    if (cancel) cancel.disabled = !disable;
    if (load) load.disabled = disable;
    if (methodSel) methodSel.disabled = disable;

    const bar = document.getElementById('wipe-progress-bar');
    if (bar && isError) bar.classList.add('error');
}

// Automatically recommend a wipe method based on selected drive types
function updateRecommendedMethod() {
    const wipeMethodSelect = document.getElementById('wipe-method-select');
    if (!wipeMethodSelect) return;
    const selectedCbs = Array.from(document.querySelectorAll('input[name="selected_drive_wipe"]:checked'));
    if (selectedCbs.length === 0) return;
    const types = selectedCbs.map(cb => {
        const row = cb.closest('tr');
        return row ? row.cells[4].textContent.trim() : '';
    });
    const uniq = [...new Set(types)];
    let recommended = '';
    if (uniq.length === 1) {
        const t = uniq[0];
        if (t === 'NVMe') recommended = 'nvme_sanitize';
        else if (t === 'SSD') recommended = 'ata_secure_erase';
        else recommended = 'zero_fill';
    } else {
        recommended = 'zero_fill';
    }
    if (wipeMethodSelect.value !== recommended) {
        wipeMethodSelect.value = recommended;
        wipeMethodSelect.dispatchEvent(new Event('change'));
    }
}


async function cancelWipe() {
    if (!confirm('Attempt to cancel the wipe?')) return;
    addWipeLogMessage('Attempting to cancel wipe...', 'warn');
    try {
        const response = await fetch('/api/wipe_drives/cancel', { method: 'POST' });
        const result = await response.json();
        if (!response.ok) throw new Error(result.error || 'Failed to cancel');
        addWipeLogMessage(result.message || 'Cancel request sent.', 'info');
        stopPolling();
    } catch (e) {
        console.error(e);
        addWipeLogMessage(e.message, 'error');
    }
}
