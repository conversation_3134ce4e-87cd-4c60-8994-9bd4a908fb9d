// Utility functions shared across frontend modules

/**
 * Fetch JSON data from the backend API.
 * Automatically throws an Error on non-2xx responses and returns `null` on network failure.
 * @param {string} url The URL to request
 * @returns {Promise<any|null>} Parsed JSON object or null if the request failed
 */
export async function fetchData(url) {
    try {
        const response = await fetch(url);
        // Treat 404 as a benign "not found" and return null quietly
        if (response.status === 404) {
            return null;
        }
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error("Could not fetch data from " + url + ":", error);
        return null; // Gracefully degrade caller logic
    }
}

/**
 * Post JSON data to the backend API.
 * @param {string} url The URL to post to
 * @param {any} data The data to send as JSON
 * @returns {Promise<any|null>} Parsed JSON response or null if the request failed
 */
export async function postData(url, data) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error("Could not post data to " + url + ":", error);
        return null;
    }
}
