/**
 * Modern Dashboard Initialization Script
 * Integrates modern system info and device conditions components
 */

import { initModernSystemInfo } from './modules/modern_system_info.js';
import { initModernDeviceConditions } from './modules/modern_device_conditions.js';

// Prevent double-initialization if main.js already handles it
if (!window.__modernDashboardBootstrapped) {
    window.__modernDashboardBootstrapped = true;
    // Initialize modern dashboard components
    document.addEventListener('DOMContentLoaded', async () => {
        console.log('Initializing modern dashboard...');
        
        try {
            // Initialize modern system info
            const systemInfo = await initModernSystemInfo();
            console.log('Modern system info initialized');
            
            // Initialize modern device conditions
            const deviceConditions = await initModernDeviceConditions();
            console.log('Modern device conditions initialized');
            
            // Add modern theme class to body
            document.body.classList.add('modern-theme');
            
            // Optional: Add refresh button functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    systemInfo.loadSystemInfo();
                });
            }
            
            console.log('Modern dashboard fully initialized');
        } catch (error) {
            console.error('Failed to initialize modern dashboard:', error);
        }
    });
} else {
    console.debug('modern_dashboard_init: Skipping; already bootstrapped elsewhere');
}

// Export for debugging
window.modernDashboard = {
    async refreshSystemInfo() {
        const systemInfo = await initModernSystemInfo();
        return systemInfo.loadSystemInfo();
    },
    async refreshDeviceConditions() {
        const deviceConditions = await initModernDeviceConditions();
        return deviceConditions.loadDeviceConditions();
    }
};
