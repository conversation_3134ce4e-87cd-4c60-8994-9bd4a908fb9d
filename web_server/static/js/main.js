import { loadSystemInfo } from './modules/system_info.js';
import { setupDriveWipe as setupDriveWipeModule } from './modules/drive_wipe.js';
import { fetchData } from './modules/utils.js';
import { setupProfileManagement } from './modules/profile_management.js';
import { setupResultsViewer } from './modules/results_viewer.js';
import { setupVisualTestLinks } from './modules/visual_tests.js';
import { setupDeviceConditionsModule } from './modules/device_conditions.js';
import { setupTestExecution, checkRunTestButtonState } from './modules/test_execution.js';
import { setupLicenseStatus } from './modules/license_status.js';
import { initModernSystemInfo } from './modules/modern_system_info.js';
import { initModernDeviceConditions } from './modules/modern_device_conditions.js';

console.log("Crucible UI Loaded");

// Defaults provided by backend; populated ASAP so later code can use.
const settingsDefaults = {};
fetch('/api/settings')
    .then(r => r.ok ? r.json() : {})
    .then(cfg => Object.assign(settingsDefaults, cfg))
    .catch(err => console.warn('Could not load backend settings defaults', err));

document.addEventListener('DOMContentLoaded', async () => {
    // Initialize UI components
    initializeUI();

    // Setup core modules
    setupTestExecution();
    setupProfileManagement(checkRunTestButtonState); // Profile management needs to trigger button state checks
    
    // Initialize enhanced results viewer if available, otherwise use standard
    if (typeof window.enhancedResultsViewer !== 'undefined') {
        window.enhancedResultsViewer.setup();
        console.log('Enhanced Results Viewer initialized');
    } else {
        setupResultsViewer();
        console.log('Standard Results Viewer initialized');
    }
    
    setupDriveWipeModule();
    setupVisualTestLinks();
    setupLicenseStatus();

    // Quick profile controls are always visible now; no header toggle wiring needed

    // Initialize modern components (replaces old system info and device conditions)
    try {
        if (!window.__modernDashboardBootstrapped) {
            window.__modernDashboardBootstrapped = true;
            await initModernSystemInfo();
            await initModernDeviceConditions();
            console.log('Modern dashboard components initialized');
        } else {
            console.debug('main.js: Skipping modern init (already bootstrapped)');
        }
    } catch (error) {
        console.error('Failed to initialize modern components, falling back to legacy:', error);
        // Fallback to legacy components
        loadSystemInfo();
        setupDeviceConditionsModule();
    }

    // Load UI/UX enhancements (preserves existing design)
    loadCrucibleEnhancements();
});

function initializeUI() {
    const collapsibleSections = document.querySelectorAll('.collapsible');
    collapsibleSections.forEach(section => {
        section.classList.remove('expanded');
    });
}

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.toggle('expanded');
    }
}

// Expose the toggle function globally so inline onclick handlers in index.html can access it
window.toggleSection = toggleSection;

/**
 * Load crucible UI/UX enhancements without changing existing design
 */
function loadCrucibleEnhancements() {
    try {
        // Load enhancement script dynamically to avoid breaking existing functionality
        const script = document.createElement('script');
        script.src = '/static/js/crucible_enhancements.js';
        script.async = true;
        script.onerror = () => {
            console.log('Crucible enhancements not available - continuing with standard UI');
        };
        document.head.appendChild(script);
    } catch (error) {
        console.log('Enhancement loading failed gracefully:', error);
        // Continue without enhancements - preserve existing functionality
    }
}
