/* Modern Dashboard Redesign for Crucible */

/* Override dashboard grid to make panels full width */
.dashboard-grid {
    display: flex !important;
    flex-direction: row !important;
    gap: 1rem !important;
    width: 100% !important;
    max-width: none !important;
    align-items: stretch !important;
}

.dashboard-grid > * {
    flex: 1 !important;
    min-width: 0 !important;
}

/* Responsive dashboard layout */
@media (max-width: 1200px) {
    .dashboard-grid {
        flex-direction: column !important;
        max-width: 800px !important;
        gap: 1.5rem !important;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        padding: 0 1rem !important;
        gap: 1rem !important;
    }
}

/* System Info Card - Modern Design */
#system-info {
    background: linear-gradient(135deg, #1a1f2e 0%, #151925 100%);
    border: 1px solid rgba(85, 101, 247, 0.2);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    flex: 1 !important;
    min-width: 0 !important;
    width: auto !important;
}

#system-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent, 
        #5565f7 20%, 
        #7c94f7 50%, 
        #5565f7 80%, 
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.3; transform: translateX(-100%); }
    50% { opacity: 1; transform: translateX(100%); }
}

#system-info h2 {
    background: linear-gradient(135deg, #5565f7, #7c94f7);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.3px;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(85, 101, 247, 0.2);
}

#system-info-content {
    flex: 1;
    width: 100%;
}

/* Ensure padding/borders are included in width calculations within System Info */
#system-info, 
#system-info *,
#system-info *::before,
#system-info *::after {
    box-sizing: border-box;
}

/* Modern System Info Grid */
.modern-sysinfo {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.6rem;
    padding: 0.4rem;
    width: 100%;
    max-width: none;
    overflow-x: hidden; /* guard against tiny overflow from child rounding */
}

.sysinfo-card {
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid rgba(85, 101, 247, 0.2);
    border-radius: 8px;
    padding: 0.6rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 70px;
    /* Let the card size to its grid cell without exceeding it */
    width: auto;
    max-width: 100%;
}

.sysinfo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(85, 101, 247, 0.15);
    border-color: rgba(85, 101, 247, 0.3);
    background: rgba(30, 34, 45, 0.8);
}

.sysinfo-card__icon {
    position: absolute;
    top: 0.9rem;
    right: 0.9rem;
    width: 24px;
    height: 24px;
    opacity: 0.4;
    transition: opacity 0.3s;
}

.sysinfo-card:hover .sysinfo-card__icon {
    opacity: 0.7;
}

.sysinfo-card__label {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #8892b0;
    margin-bottom: 0.15rem;
    font-weight: 500;
}

.sysinfo-card__value {
    font-size: 0.9rem;
    color: #e6f1ff;
    font-weight: 600;
    line-height: 1.2;
    word-break: break-word;
}

.sysinfo-card__value--highlight {
    color: #64ffda;
    font-weight: 600;
}

/* Accent colors for different hardware categories */
.sysinfo-card--cpu {
    border-left: 3px solid #4a90e2;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), transparent);
}

.sysinfo-card--ram {
    border-left: 3px solid #4ae287;
    background: linear-gradient(135deg, rgba(74, 226, 135, 0.05), transparent);
}

.sysinfo-card--gpu {
    border-left: 3px solid #904ae2;
    background: linear-gradient(135deg, rgba(144, 74, 226, 0.05), transparent);
}

.sysinfo-card--disk {
    border-left: 3px solid #e24a90;
    background: linear-gradient(135deg, rgba(226, 74, 144, 0.05), transparent);
}

.sysinfo-card--battery {
    border-left: 3px solid #ff9800;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.05), transparent);
}

.sysinfo-card--display {
    border-left: 3px solid #90e24a;
    background: linear-gradient(135deg, rgba(144, 226, 74, 0.05), transparent);
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.2rem 0.6rem;
    border-radius: 20px;
    font-size: 0.78rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
}

.status-badge--ok {
    background: rgba(74, 226, 135, 0.15);
    color: #4ae287;
    border: 1px solid rgba(74, 226, 135, 0.3);
}

.status-badge--warning {
    background: rgba(255, 152, 0, 0.15);
    color: #ff9800;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-badge--error {
    background: rgba(244, 67, 54, 0.15);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Device Conditions - Modern Design */
#device-conditions {
    background: linear-gradient(135deg, #1a2332 0%, #152028 100%);
    border: 1px solid rgba(52, 152, 219, 0.2);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    flex: 1 !important;
    min-width: 0 !important;
    width: auto !important;
    display: flex;
    flex-direction: column;
}

#device-conditions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        #3498db 20%,
        #5dade2 50%,
        #3498db 80%,
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

#device-conditions h2 {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 0.3px;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(52, 152, 219, 0.2);
}

#dc-form-area {
    flex: 1;
    width: 100%;
    height: 100%;
    min-width: 0 !important;
}

/* Ensure integrated Device Conditions container is not a grid so inner form can fill full width */
#device-conditions .device-conditions-form {
    display: block !important;
    width: 100% !important;
}

/* Modern Device Conditions Form */
.dc-modern-form {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    grid-template-rows: auto auto auto auto auto 1fr;
    gap: 0.5rem;
    padding: 0.4rem;
    width: 100%;
    max-width: none;
    flex: 1;
    overflow: visible;
}

/* Flat form layout without category cards */
.dc-modern-form--flat {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1rem !important;
    padding: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Specific category positioning */
.dc-category-card[data-category="Physical Condition"] {
    grid-column: 1;
    grid-row: 1;
}

.dc-category-card[data-category="Screen Condition"] {
    grid-column: 2;
    grid-row: 1;
}

.dc-category-card[data-category="Keyboard Condition"] {
    grid-column: 1;
    grid-row: 2;
}

.dc-category-card[data-category="Touchpad Condition"] {
    grid-column: 2;
    grid-row: 2;
}

.dc-category-card[data-category="USB Ports"] {
    grid-column: 1;
    grid-row: 3;
}

.dc-category-card[data-category="Missing Items & Grade"] {
    grid-column: 2;
    grid-row: 3;
}

.dc-category-card[data-category="Notes"] {
    grid-column: 1 / -1;
    grid-row: 4;
    min-height: 120px;
}

/* Responsive adjustments */
@media (max-width: 900px) {
    .dc-modern-form {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
    
    .dc-category-card[data-category="Physical Condition"],
    .dc-category-card[data-category="Screen Condition"],
    .dc-category-card[data-category="Keyboard Condition"],
    .dc-category-card[data-category="Touchpad Condition"],
    .dc-category-card[data-category="USB Ports"],
    .dc-category-card[data-category="Missing Items & Grade"],
    .dc-category-card[data-category="Notes"] {
        grid-column: 1;
        grid-row: auto;
    }
}

/* Custom scrollbar */
.dc-modern-form::-webkit-scrollbar {
    width: 6px;
}

.dc-modern-form::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 3px;
}

.dc-modern-form::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3498db, #5dade2);
    border-radius: 3px;
}

.dc-modern-form::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #2980b9, #3498db);
}

.dc-category-card {
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    width: 100%;
    min-height: 80px;
}

.dc-category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
    border-color: rgba(52, 152, 219, 0.3);
    background: rgba(30, 34, 45, 0.8);
}

.dc-category-card__title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding-bottom: 0.3rem;
    border-bottom: 1px solid rgba(52, 152, 219, 0.3);
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    opacity: 0.7;
}

.dc-category-card__icon {
    width: 18px;
    height: 18px;
    opacity: 0.7;
}

.dc-field {
    margin-bottom: 0.0rem;
}

/* Flat form field styling */
.dc-modern-form--flat .dc-field {
    background: rgb(20, 25, 35);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 8px;
    padding: 0.25rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    margin-bottom: 0;
    min-height: 120px;
    flex: 1 1 calc(33.333% - 1rem) !important;
    min-width: 200px !important;
    box-sizing: border-box !important;
}

.dc-modern-form--flat .dc-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
    border-color: rgba(52, 152, 219, 0.3);
    background: rgba(30, 34, 45, 0.8);
}

.dc-field__label {
    display: block;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #8892b0;
    margin-bottom: 0.3rem;
    font-weight: 600;
}

/* Flat form field label styling */
.dc-modern-form--flat .dc-field__label {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
    font-weight: 700;
}

/* Special handling for Notes field to span full width */
.dc-modern-form--flat .dc-field[data-field="Notes"] {
    flex: 1 1 100% !important;
}

/* Responsive adjustments for flat form */
@media (max-width: 900px) {
    .dc-modern-form--flat .dc-field {
        flex: 1 1 100% !important;
    }
}

.dc-field__input {
    width: 100%;
    padding: 0.35rem;
    background: rgba(30, 35, 45, 0.8);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 4px;
    color: #e6f1ff;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    box-sizing: border-box; /* prevent overflow from padding/border */
}

/* Flat form input styling */
.dc-modern-form--flat .dc-field__input {
    padding: 0.75rem;
    font-size: 0.9rem;
    border-radius: 6px;
    min-height: 44px;
}

.dc-modern-form--flat .dc-field__input--textarea {
    min-height: 80px;
    resize: vertical;
}

.dc-modern-form--flat .dc-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dc-modern-form--flat .dc-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #e6f1ff;
    cursor: pointer;
    padding: 0.25rem 0;
}

.dc-field__input:focus {
    outline: none;
    border-color: #904ae2;
    box-shadow: 0 0 0 3px rgba(144, 74, 226, 0.1);
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
}

.dc-field__input:hover {
    border-color: rgba(144, 74, 226, 0.3);
}

/* Select styling */
.dc-field__input--select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%238892b0' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    padding-right: 2rem;
}

/* Ensure dark background for selects and their dropdowns within Device Conditions */
#device-conditions .dc-field__input--select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: rgba(30, 35, 45, 0.95);
    color: #e6f1ff;
}

#device-conditions .dc-field__input--select option,
#device-conditions .dc-field__input--select optgroup {
    background-color: #1a2332;
    color: #e6f1ff;
}

#device-conditions .dc-field__input--select option:checked,
#device-conditions .dc-field__input--select option:hover {
    background-color: #0f1522;
    color: #e6f1ff;
}

/* Compact spacing for Device Conditions fields */
#device-conditions .dc-modern-form {
    gap: 0.35rem;
}

#device-conditions .dc-category-card {
    padding: 0.4rem;
}

#device-conditions .dc-field {
    margin-bottom: 0.15rem;
}

#device-conditions .dc-field__label {
    margin-bottom: 0.2rem;
}

#device-conditions .dc-field__input {
    padding: 0.28rem 0.4rem;
    font-size: 0.68rem;
    line-height: 1.2;
}

/* Compact overrides for flat variant inside Device Conditions */
#device-conditions .dc-modern-form--flat .dc-field {
    min-height: 80px;
    padding: 0.75rem;
}

#device-conditions .dc-modern-form--flat .dc-field__input {
    min-height: 36px;
    padding: 0.4rem 0.5rem;
}

#device-conditions .dc-category-card__title {
    margin-bottom: 0.3rem;
    padding-bottom: 0.2rem;
}

/* Notes field layout fixes for legacy dc-category markup */
#device-conditions .dc-category.dc-notes .dc-field__label,
#device-conditions .dc-category.dc-notes label {
    display: block !important;
    margin-bottom: 0.35rem !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    color: #8892b0 !important;
}

#device-conditions .dc-category.dc-notes textarea,
#device-conditions textarea#dc-input-Notes {
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    box-sizing: border-box !important;
    padding: 0.5rem 0.6rem !important;
    background: rgba(30, 35, 45, 0.8) !important;
    border: 1px solid rgba(52, 152, 219, 0.2) !important;
    border-radius: 6px !important;
    color: #e6f1ff !important;
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
    resize: vertical !important;
    min-height: 64px !important;
}

/* Ensure the Notes card content doesn't overflow its rounded container */
#device-conditions .dc-category.dc-notes {
    overflow: hidden;
}

/* High-compact layout for Device Conditions flat form */
#device-conditions .dc-modern-form--flat {
    /* Flex layout so rows fill available space; max 3 items per row via basis */
    --dc-gap: 0.6rem;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: var(--dc-gap) !important;
    padding: 0.6rem !important;
    width: 100% !important;
    align-items: stretch !important;
}

#device-conditions .dc-modern-form--flat .dc-field {
    /* Card-like styling similar to .sysinfo-card */
    display: block !important;
    background: rgba(20, 25, 35, 0.9) !important;
    border: 1px solid rgba(52, 152, 219, 0.2) !important;
    border-radius: 8px !important;
    padding: 0.6rem !important;
    min-height: 70px !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow: hidden !important; /* clip inner control corners to match radius */
    /* 3 per row by default, grow to fill leftover space on last row */
    flex: 1 1 calc((100% - (2 * var(--dc-gap))) / 3) !important;
    min-width: 180px !important;
}
/* Flex layout auto-fills remaining space; no special last-row rules needed */

#device-conditions .dc-modern-form--flat .dc-field__label {
    display: block !important;
    font-size: 0.8rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    color: #8892b0 !important;
    margin-bottom: 0.3rem !important;
    line-height: 1.2 !important;
}

#device-conditions .dc-modern-form--flat .dc-field__input {
    width: 100% !important;
    min-height: 30px !important;
    padding: 0.3rem 0.5rem !important;
    font-size: 0.75rem !important;
    box-sizing: border-box !important; /* ensure width includes padding/border */
}

#device-conditions .dc-modern-form--flat .dc-field__input--textarea {
    min-height: 60px !important;
    resize: vertical !important;
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    margin: 0 !important; /* prevent default textarea margins causing overflow */
}

#device-conditions .dc-modern-form--flat .dc-checkbox-group {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0.4rem !important;
    flex-wrap: wrap !important;
}

#device-conditions .dc-modern-form--flat .dc-checkbox-label {
    padding: 0 !important;
    font-size: 0.75rem !important;
}

/* Slightly tighter select arrow spacing in compact mode */
#device-conditions .dc-modern-form--flat .dc-field__input--select {
    padding-right: 1.5rem !important;
    background-position: right 0.5rem center !important;
}

/* Even tighter spacing to eliminate scrollbars where possible */
#device-conditions h2 {
    margin-bottom: 0.2rem !important;
    padding-bottom: 0.2rem !important;
    font-size: 0.9rem !important;
    border-bottom: 1px solid rgba(52, 152, 219, 0.15) !important;
}

#device-conditions .dc-modern-form,
#device-conditions .dc-modern-form--flat {
    gap: 0.6rem !important;
    padding: 0.6rem !important;
}

#device-conditions .dc-modern-form--flat .dc-field {
    padding: 0.15rem 0.25rem !important;
    gap: 0.25rem !important;
}

#device-conditions .dc-modern-form--flat .dc-field__input {
    min-height: 28px !important;
    padding: 0.2rem 0.4rem !important;
    font-size: 0.78rem !important;
}

#device-conditions .dc-modern-form--flat .dc-field__input--textarea {
    min-height: 38px !important;
}

#device-conditions .dc-modern-form--flat .dc-field__input--select {
    padding-right: 1.5rem !important;
}

#device-conditions .dc-modern-form--flat .dc-checkbox-label {
    font-size: 0.75rem !important;
}

/* Ensure Notes does not force full-width within Device Conditions */
#device-conditions .dc-modern-form--flat .dc-field[data-field="Notes"] {
    flex: 1 1 calc((100% - (2 * var(--dc-gap))) / 3) !important;
}

/* Last-row fill rules for 3-per-row flex layout */
/* If exactly 1 item remains, make it full width */
#device-conditions .dc-modern-form--flat > .dc-field:nth-last-child(1):nth-child(3n + 1) {
    flex: 1 1 100% !important;
}

/* If exactly 2 items remain, make them each half width */
#device-conditions .dc-modern-form--flat > .dc-field:nth-last-child(2):nth-child(3n + 1),
#device-conditions .dc-modern-form--flat > .dc-field:nth-last-child(1):nth-child(3n + 2) {
    flex: 1 1 calc(50% - (var(--dc-gap) / 2)) !important;
}

/* Prevent horizontal overflow within device conditions */
#device-conditions,
#device-conditions .dc-modern-form {
    overflow-x: hidden !important;
}

/* Modern buttons */
.dc-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: auto;
    padding: 0.75rem 0.5rem 0.5rem 0.5rem;
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
}

.btn-modern {
    padding: 0.4rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.3px;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: width 0.6s, height 0.6s;
    transform: translate(-50%, -50%);
}

.btn-modern:hover::before {
    width: 300px;
    height: 300px;
}

.btn-modern--primary {
    background: linear-gradient(135deg, #3498db, #5dade2);
    border: 1px solid rgba(52, 152, 219, 0.3);
    color: white;
}

.btn-modern--primary:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    border-color: rgba(52, 152, 219, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-modern--success {
    background: linear-gradient(135deg, #4ae287, #6ae2a7);
    color: #0a0f0c;
    box-shadow: 0 4px 15px rgba(74, 226, 135, 0.3);
}

.btn-modern--success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 226, 135, 0.4);
}

/* Status message modern */
.status-message-modern {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.status-message-modern--success {
    background: rgba(74, 226, 135, 0.1);
    color: #4ae287;
    border: 1px solid rgba(74, 226, 135, 0.2);
}

.status-message-modern--error {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.2);
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-top-color: #5565f7;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-sysinfo {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
    
    .dc-modern-form {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    }
}

@media (max-width: 768px) {
    .modern-sysinfo {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.6rem;
    }
    
    .dc-modern-form {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.6rem;
    }
    
    .sysinfo-card {
        padding: 0.85rem;
        min-height: 100px;
    }
    
    .dc-category-card {
        padding: 0.85rem;
        min-height: 130px;
    }
}

@media (min-width: 1400px) {
    .modern-sysinfo {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 0.75rem;
    }
    
    .dc-modern-form {
        grid-template-columns: 1.2fr 0.8fr;
        gap: 0.75rem;
    }
    
    .sysinfo-card {
        padding: 0.75rem;
        min-height: 80px;
    }
    
    .dc-category-card {
        padding: 0.75rem;
        min-height: 100px;
    }
}

/* Disk info special styling */
.disk-info-grid {
    display: grid;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.disk-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0.6rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    font-size: 0.85rem;
}

.disk-item__name {
    color: #8892b0;
    font-weight: 500;
}

.disk-item__details {
    color: #e6f1ff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Icon styles */
.icon-cpu::before { content: "⚡"; }
.icon-ram::before { content: "💾"; }
.icon-gpu::before { content: "🎮"; }
.icon-disk::before { content: "💿"; }
.icon-battery::before { content: "🔋"; }
.icon-display::before { content: "🖥️"; }
.icon-physical::before { content: "📦"; }
.icon-screen::before { content: "📱"; }
.icon-keyboard::before { content: "⌨️"; }
.icon-touchpad::before { content: "🖱️"; }
.icon-ports::before { content: "🔌"; }

/* --- Drive Wipe Section Styling (cool blues, no yellow/orange) --- */
#drive-wipe-section {
    background: linear-gradient(135deg, #1a2332 0%, #151b28 100%);
    border: 1px solid rgba(85, 101, 247, 0.22);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.04);
}

#drive-wipe-section h2 {
    background: linear-gradient(135deg, #5565f7, #7c94f7);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    font-size: 1.05rem;
    letter-spacing: 0.3px;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(85, 101, 247, 0.22);
}

#drive-wipe-section .drive-wipe-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

/* Replace orange-y warning in this section with a cool red tone */
#drive-wipe-section .btn-warning {
    background: linear-gradient(135deg, #e35d6a, #c93e49);
    border: 1px solid rgba(227, 93, 106, 0.4);
    color: #fff;
}
#drive-wipe-section .btn-warning:hover {
    background: linear-gradient(135deg, #d94b59, #b2333d);
    box-shadow: 0 4px 14px rgba(227, 93, 106, 0.35);
}

#drive-wipe-section .btn-danger {
    background: linear-gradient(135deg, #ff4d4d, #e23b3b);
    border: 1px solid rgba(255, 77, 77, 0.35);
    color: #fff;
}
#drive-wipe-section .btn-danger:hover {
    background: linear-gradient(135deg, #e84444, #cb3333);
    box-shadow: 0 4px 14px rgba(255, 77, 77, 0.3);
}

/* Progress bars (overall + per-drive) */
#drive-wipe-section .progress-bar-container {
    height: 16px;
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(85, 101, 247, 0.25);
    border-radius: 2px;
    overflow: hidden;
}
#drive-wipe-section .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #5565f7, #7c94f7);
    color: #0b1020;
    font-weight: 700;
    text-align: center;
    border-radius: 0;
}
#drive-wipe-section .progress-bar.error {
    background: linear-gradient(90deg, #f44336, #e53935);
    color: #fff;
}

/* Per-drive compact bars */
#drive-wipe-section .progress-bar-container-small {
    height: 8px;
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(85, 101, 247, 0.22);
    border-radius: 2px;
    overflow: hidden;
}
#drive-wipe-section .progress-bar-small {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #4f85ff, #80a8ff);
    transition: width 0.25s ease;
    border-radius: 0;
}
#drive-wipe-section .progress-bar-small.status-complete {
    background: linear-gradient(90deg, #2ecc71, #6be09c);
}
#drive-wipe-section .progress-bar-small.status-failed {
    background: linear-gradient(90deg, #f44336, #e57373);
}

#drive-wipe-section .progress-meta {
    display: flex;
    gap: 0.5rem;
    font-size: 0.78rem;
    color: #aab4d4;
    margin-top: 0.25rem;
}
#drive-wipe-section .eta-text { color: #aab4d4; }
#drive-wipe-section .rate-text { color: #91a0d6; }

/* Table header and status colors */
#drive-wipe-section .drive-wipe-table thead th {
    position: sticky;
    top: 0;
    background: #111827;
    z-index: 1;
}

#drive-wipe-section .drive-status-cell { color: #c7d2fe; }
#drive-wipe-section .drive-status-cell.status-wiping { color: #7aa2ff; }
#drive-wipe-section .drive-status-cell.status-pass,
#drive-wipe-section .drive-status-cell.status-complete { color: #7ee4a8; }
#drive-wipe-section .drive-status-cell.status-fail,
#drive-wipe-section .drive-status-cell.status-error { color: #ff8a8a; }
